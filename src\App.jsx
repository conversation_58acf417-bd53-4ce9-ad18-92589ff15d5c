import React, { useState, useRef } from 'react';
import { Sidebar } from './components/Sidebar';
import { NodeEditPanel } from './components/NodeEditPanel';
import { CanvasRenderer } from './components/CanvasRenderer';
import { useMapData } from './hooks/useMapData';
import { useRoadData } from './hooks/useRoadData';

// ====================================================================================
// --- FILE: src/App.js ---
// 重构后的工厂数字孪生系统主应用组件
// ====================================================================================

// 默认方案配置
const DEFAULT_SCHEME = {
    id: 'default-scheme',
    name: '工厂布局方案 A',
    description: '标准生产线布局，适用于中小型制造',
    mapDataUrl: '/map_data.json',
    roadDataUrl: '/road_data.json',
    status: 'active'
};

function FactoryCanvasApp() {
    // 当前选择的方案（默认加载预设方案）
    const [currentScheme, setCurrentScheme] = useState(DEFAULT_SCHEME);

    // 加载地图和道路数据
    const { mapData, isLoading: mapLoading } = useMapData(currentScheme.mapDataUrl);
    const { roadData, isLoading: roadLoading } = useRoadData(currentScheme.roadDataUrl);

    // 界面状态
    const [selectedElement, setSelectedElement] = useState(null);
    const [isAddingMode, setIsAddingMode] = useState(false);
    const [sidebarCollapsed, setSidebarCollapsed] = useState(() => {
        // 从localStorage读取折叠状态
        const saved = localStorage.getItem('sidebar_collapsed');
        return saved ? JSON.parse(saved) : false;
    });

    // 图层显示控制
    const [layerSettings, setLayerSettings] = useState(() => {
        const saved = localStorage.getItem('layer_settings');
        return saved ? JSON.parse(saved) : {
            showRoads: true,
            showNodes: true,
            showEdges: true,
            roadOpacity: 0.8,
            nodeOpacity: 1.0,
            edgeOpacity: 1.0
        };
    });

    // 处理侧边栏折叠切换
    const handleToggleSidebar = () => {
        const newCollapsed = !sidebarCollapsed;
        setSidebarCollapsed(newCollapsed);
        localStorage.setItem('sidebar_collapsed', JSON.stringify(newCollapsed));
    };

    // 处理图层设置更新
    const handleLayerSettingsChange = (newSettings) => {
        const updatedSettings = { ...layerSettings, ...newSettings };
        setLayerSettings(updatedSettings);
        localStorage.setItem('layer_settings', JSON.stringify(updatedSettings));
    };

    // 视图控制引用
    const canvasControlsRef = useRef(null);

    // 事件处理函数
    const handleElementClick = (element) => {
        setSelectedElement(element);
        // 如果在新增模式下点击了路线，添加新节点
        if (isAddingMode && element?.type === 'edge') {
            handleAddNodeToRoute(element);
        }
    };

    const handleAddNodeToRoute = (routeElement) => {
        // 模拟在路线上添加新节点
        const newNodeId = `node-${Date.now()}`;
        const newNode = {
            id: newNodeId,
            x: routeElement.data.x || 0,
            y: routeElement.data.y || 0,
            type: 'workstation',
            name: `新节点 ${newNodeId.slice(-4)}`
        };

        console.log('添加新节点:', newNode);
        // 这里可以添加实际的节点添加逻辑
        setIsAddingMode(false);
    };

    const handleToggleAddMode = () => {
        setIsAddingMode(!isAddingMode);
        setSelectedElement(null);
    };

    const handleSchemeChange = (scheme) => {
        setCurrentScheme(scheme);
        setSelectedElement(null);
    };

    // 处理高亮元素（从异常检查跳转）
    const handleHighlightElement = (element) => {
        setSelectedElement(element);
        // 可以添加视图跳转逻辑，比如移动到元素位置
        if (canvasControlsRef.current?.focusOnElement) {
            canvasControlsRef.current.focusOnElement(element);
        }
    };

    // 视图控制函数
    const handleZoomIn = () => canvasControlsRef.current?.zoomIn?.();
    const handleZoomOut = () => canvasControlsRef.current?.zoomOut?.();
    const handleResetView = () => canvasControlsRef.current?.resetView?.();

    // 如果正在加载数据，显示加载界面
    if (mapLoading || roadLoading) {
        return (
            <div className="h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">正在加载方案数据...</p>
                    <p className="text-sm text-gray-500 mt-2">{currentScheme.name}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="h-screen flex bg-gray-50">
            {/* 左侧可折叠导航栏 */}
            <Sidebar
                collapsed={sidebarCollapsed}
                onToggleCollapse={handleToggleSidebar}
                currentScheme={currentScheme}
                onSchemeChange={handleSchemeChange}
                onZoomIn={handleZoomIn}
                onZoomOut={handleZoomOut}
                onResetView={handleResetView}
                isAddingMode={isAddingMode}
                onToggleAddMode={handleToggleAddMode}
                layerSettings={layerSettings}
                onLayerSettingsChange={handleLayerSettingsChange}
                mapData={mapData}
                onHighlightElement={handleHighlightElement}
            />

            {/* 中间地图区域 */}
            <main className="flex-grow relative bg-white">
                <CanvasRenderer
                    ref={canvasControlsRef}
                    mapData={mapData}
                    roadData={roadData}
                    selectedElement={selectedElement}
                    onElementClick={handleElementClick}
                    isAddingMode={isAddingMode}
                    layerSettings={layerSettings}
                />
            </main>

            {/* 右侧节点编辑面板 */}
            <NodeEditPanel
                selectedElement={selectedElement}
                onElementUpdate={(updatedElement) => {
                    console.log('更新元素:', updatedElement);
                    // 这里可以添加实际的更新逻辑
                }}
                onElementDelete={(elementId) => {
                    console.log('删除元素:', elementId);
                    setSelectedElement(null);
                    // 这里可以添加实际的删除逻辑
                }}
            />
        </div>
    );
}


// --- 应用的主导出 ---
export default function App() {
    return (
        <div style={{ width: '100vw', height: '100vh' }}>
            <FactoryCanvasApp />
        </div>
    );
}

