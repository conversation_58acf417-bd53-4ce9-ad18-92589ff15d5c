import { useState, useEffect } from 'react';

// 图标组件
const EditIcon = () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
    </svg>
);

const DeleteIcon = () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
    </svg>
);

const SaveIcon = () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
    </svg>
);

const CancelIcon = () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
    </svg>
);

export const NodeEditPanel = ({ selectedElement, onElementUpdate, onElementDelete }) => {
    const [isEditing, setIsEditing] = useState(false);
    const [isCollapsed, setIsCollapsed] = useState(() => {
        // 从localStorage读取折叠状态
        const saved = localStorage.getItem('nodeEditPanel_collapsed');
        return saved ? JSON.parse(saved) : false;
    });
    const [editForm, setEditForm] = useState({
        name: '',
        type: '',
        description: '',
        x: 0,
        y: 0
    });

    // 保存折叠状态到localStorage
    const toggleCollapse = () => {
        const newCollapsed = !isCollapsed;
        setIsCollapsed(newCollapsed);
        localStorage.setItem('nodeEditPanel_collapsed', JSON.stringify(newCollapsed));
    };

    // 当选中元素改变时，重置编辑状态
    useEffect(() => {
        setIsEditing(false);
        if (selectedElement?.data) {
            setEditForm({
                name: selectedElement.data.name || selectedElement.data.id || '',
                type: selectedElement.data.type || 'unknown',
                description: selectedElement.data.description || '',
                x: selectedElement.data.x || 0,
                y: selectedElement.data.y || 0
            });
        }
    }, [selectedElement]);

    const handleEdit = () => {
        setIsEditing(true);
    };

    const handleSave = () => {
        if (selectedElement) {
            onElementUpdate({
                ...selectedElement,
                data: {
                    ...selectedElement.data,
                    ...editForm
                }
            });
        }
        setIsEditing(false);
    };

    const handleCancel = () => {
        if (selectedElement?.data) {
            setEditForm({
                name: selectedElement.data.name || selectedElement.data.id || '',
                type: selectedElement.data.type || 'unknown',
                description: selectedElement.data.description || '',
                x: selectedElement.data.x || 0,
                y: selectedElement.data.y || 0
            });
        }
        setIsEditing(false);
    };

    const handleDelete = () => {
        if (selectedElement && window.confirm('确定要删除这个元素吗？')) {
            onElementDelete(selectedElement.data.id);
        }
    };

    const getElementTypeText = (type) => {
        const typeMap = {
            'node': '节点',
            'edge': '连接线',
            'road_point': '道路点',
            'storage': '存储区',
            'workstation': '工作站',
            'loading_dock': '装卸台',
            'intersection': '交叉口'
        };
        return typeMap[type] || type || '未知';
    };

    const getElementTypeColor = (type) => {
        const colorMap = {
            'storage': 'bg-blue-100 text-blue-800',
            'workstation': 'bg-green-100 text-green-800',
            'loading_dock': 'bg-yellow-100 text-yellow-800',
            'intersection': 'bg-purple-100 text-purple-800',
            'edge': 'bg-gray-100 text-gray-800',
            'road_point': 'bg-orange-100 text-orange-800'
        };
        return colorMap[type] || 'bg-gray-100 text-gray-800';
    };

    if (!selectedElement) {
        return (
            <div className={`bg-white border-l border-gray-200 transition-all duration-300 ${
                isCollapsed ? 'w-12' : 'w-80'
            }`}>
                {/* 折叠按钮 */}
                <div className="p-3 border-b border-gray-200 flex justify-end">
                    <button
                        onClick={toggleCollapse}
                        className="p-1 rounded hover:bg-gray-100 text-gray-600 transition-colors"
                        title={isCollapsed ? '展开面板' : '折叠面板'}
                    >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                d={isCollapsed ? "M9 5l7 7-7 7" : "M15 19l-7-7 7-7"} />
                        </svg>
                    </button>
                </div>

                {!isCollapsed && (
                    <div className="p-6">
                        <div className="text-center text-gray-500">
                            <div className="mb-4">
                                <svg className="w-16 h-16 mx-auto text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">未选择元素</h3>
                            <p className="text-sm text-gray-600">
                                点击地图上的节点或连接线来查看详细信息
                            </p>
                        </div>
                    </div>
                )}
            </div>
        );
    }

    const elementData = selectedElement.data;

    return (
        <div className={`bg-white border-l border-gray-200 flex flex-col transition-all duration-300 ${
            isCollapsed ? 'w-12' : 'w-80'
        }`}>
            {/* 头部 */}
            <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between mb-2">
                    {!isCollapsed && (
                        <>
                            <h2 className="text-lg font-semibold text-gray-900">元素详情</h2>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getElementTypeColor(elementData.type)}`}>
                                {getElementTypeText(elementData.type)}
                            </span>
                        </>
                    )}
                    <button
                        onClick={toggleCollapse}
                        className="p-1 rounded hover:bg-gray-100 text-gray-600 transition-colors ml-auto"
                        title={isCollapsed ? '展开面板' : '折叠面板'}
                    >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                d={isCollapsed ? "M9 5l7 7-7 7" : "M15 19l-7-7 7-7"} />
                        </svg>
                    </button>
                </div>
                {!isCollapsed && (
                    <p className="text-sm text-gray-600">
                        {elementData.name || elementData.id || '未命名元素'}
                    </p>
                )}
            </div>

            {/* 内容区域 */}
            {!isCollapsed && (
                <div className="flex-1 overflow-y-auto p-4">
                <div className="space-y-4">
                    {/* 基本信息 */}
                    <div>
                        <h3 className="text-sm font-medium text-gray-900 mb-3">基本信息</h3>
                        <div className="space-y-3">
                            <div>
                                <label className="block text-xs font-medium text-gray-700 mb-1">名称</label>
                                {isEditing ? (
                                    <input
                                        type="text"
                                        value={editForm.name}
                                        onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                ) : (
                                    <p className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
                                        {elementData.name || elementData.id || '未命名'}
                                    </p>
                                )}
                            </div>

                            <div>
                                <label className="block text-xs font-medium text-gray-700 mb-1">类型</label>
                                {isEditing ? (
                                    <select
                                        value={editForm.type}
                                        onChange={(e) => setEditForm({ ...editForm, type: e.target.value })}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="storage">存储区</option>
                                        <option value="workstation">工作站</option>
                                        <option value="loading_dock">装卸台</option>
                                        <option value="intersection">交叉口</option>
                                    </select>
                                ) : (
                                    <p className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
                                        {getElementTypeText(elementData.type)}
                                    </p>
                                )}
                            </div>

                            <div>
                                <label className="block text-xs font-medium text-gray-700 mb-1">描述</label>
                                {isEditing ? (
                                    <textarea
                                        value={editForm.description}
                                        onChange={(e) => setEditForm({ ...editForm, description: e.target.value })}
                                        rows={3}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="输入描述信息..."
                                    />
                                ) : (
                                    <p className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md min-h-[4rem]">
                                        {elementData.description || '暂无描述'}
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* 位置信息 */}
                    <div>
                        <h3 className="text-sm font-medium text-gray-900 mb-3">位置信息</h3>
                        <div className="grid grid-cols-2 gap-3">
                            <div>
                                <label className="block text-xs font-medium text-gray-700 mb-1">X 坐标</label>
                                {isEditing ? (
                                    <input
                                        type="number"
                                        value={editForm.x}
                                        onChange={(e) => setEditForm({ ...editForm, x: parseFloat(e.target.value) || 0 })}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                ) : (
                                    <p className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
                                        {Math.round(elementData.x || 0)}
                                    </p>
                                )}
                            </div>
                            <div>
                                <label className="block text-xs font-medium text-gray-700 mb-1">Y 坐标</label>
                                {isEditing ? (
                                    <input
                                        type="number"
                                        value={editForm.y}
                                        onChange={(e) => setEditForm({ ...editForm, y: parseFloat(e.target.value) || 0 })}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                ) : (
                                    <p className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
                                        {Math.round(elementData.y || 0)}
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* 其他属性 */}
                    {elementData.id && (
                        <div>
                            <h3 className="text-sm font-medium text-gray-900 mb-3">其他属性</h3>
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <span className="text-xs text-gray-600">ID:</span>
                                    <span className="text-xs text-gray-900 font-mono">{elementData.id}</span>
                                </div>
                                {selectedElement.type && (
                                    <div className="flex justify-between">
                                        <span className="text-xs text-gray-600">元素类型:</span>
                                        <span className="text-xs text-gray-900">{selectedElement.type}</span>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
                </div>
            )}

            {/* 操作按钮 */}
            {!isCollapsed && (
                <div className="p-4 border-t border-gray-200">
                {isEditing ? (
                    <div className="flex space-x-2">
                        <button
                            onClick={handleSave}
                            className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                        >
                            <SaveIcon />
                            保存
                        </button>
                        <button
                            onClick={handleCancel}
                            className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700 transition-colors"
                        >
                            <CancelIcon />
                            取消
                        </button>
                    </div>
                ) : (
                    <div className="flex space-x-2">
                        <button
                            onClick={handleEdit}
                            className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                        >
                            <EditIcon />
                            编辑
                        </button>
                        <button
                            onClick={handleDelete}
                            className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors"
                        >
                            <DeleteIcon />
                            删除
                        </button>
                    </div>
                )}
                </div>
            )}
        </div>
    );
};
