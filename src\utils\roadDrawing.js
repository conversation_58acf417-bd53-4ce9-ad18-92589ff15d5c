// 绘制道路背景（浅色显示）
export const drawRoadBackground = (ctx, road, nodes, scale) => {
    if (!road.points || road.points.length < 2) return;

    const roadNodes = road.points.map(pointId => nodes.get(pointId)).filter(Boolean);
    if (roadNodes.length < 2) return;

    ctx.save();

    // 设置道路背景样式（浅蓝色）
    ctx.fillStyle = '#E3F2FD';
    ctx.strokeStyle = '#BBDEFB';
    ctx.lineWidth = 1 / scale;

    // 绘制道路多边形
    if (roadNodes.length >= 3) {
        ctx.beginPath();
        ctx.moveTo(roadNodes[0].x, roadNodes[0].y);

        for (let i = 1; i < roadNodes.length; i++) {
            ctx.lineTo(roadNodes[i].x, roadNodes[i].y);
        }

        ctx.closePath();
        ctx.fill();
        ctx.stroke();
    }

    ctx.restore();
};

export const drawRoad = (ctx, road, nodes, scale, isSelected = false) => {
    if (!road.points || road.points.length < 2) return;

    const roadNodes = road.points.map(pointId => nodes.get(pointId)).filter(Boolean);
    if (roadNodes.length < 2) return;

    ctx.save();
    
    // 设置道路样式
    ctx.fillStyle = road.color || '#E3F2FD';
    ctx.strokeStyle = isSelected ? '#1976D2' : '#BBBBBB';
    ctx.lineWidth = isSelected ? 2 / scale : 1 / scale;

    // 绘制道路多边形
    if (roadNodes.length >= 3) {
        ctx.beginPath();
        ctx.moveTo(roadNodes[0].x, roadNodes[0].y);
        
        for (let i = 1; i < roadNodes.length; i++) {
            ctx.lineTo(roadNodes[i].x, roadNodes[i].y);
        }
        
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
    }

    // 绘制道路点
    roadNodes.forEach((node, index) => {
        ctx.beginPath();
        ctx.arc(node.x, node.y, 4 / scale, 0, 2 * Math.PI);
        ctx.fillStyle = isSelected ? '#1976D2' : '#666666';
        ctx.fill();
        
        // 显示点的索引
        ctx.fillStyle = '#FFFFFF';
        ctx.font = `${10 / scale}px Arial`;
        ctx.textAlign = 'center';
        ctx.fillText(index.toString(), node.x, node.y + 3 / scale);
    });

    ctx.restore();
};

export const detectRoadClick = (roads, nodes, mouseX, mouseY, scale) => {
    const tolerance = 15 / scale;

    for (const road of roads) {
        const roadNodes = road.points.map(pointId => nodes.get(pointId)).filter(Boolean);
        
        // 检测点击的是否为道路点
        for (let i = 0; i < roadNodes.length; i++) {
            const node = roadNodes[i];
            const distance = Math.sqrt(
                Math.pow(mouseX - node.x, 2) + Math.pow(mouseY - node.y, 2)
            );
            
            if (distance <= tolerance) {
                return {
                    type: 'road_point',
                    data: {
                        roadId: road.id,
                        pointIndex: i,
                        pointId: road.points[i],
                        x: node.x,
                        y: node.y,
                        road: road
                    }
                };
            }
        }

        // 检测点击的是否为道路边缘（用于插入新点）
        for (let i = 0; i < roadNodes.length - 1; i++) {
            const node1 = roadNodes[i];
            const node2 = roadNodes[i + 1];
            
            const distance = distanceToLineSegment(mouseX, mouseY, node1.x, node1.y, node2.x, node2.y);
            
            if (distance <= tolerance) {
                return {
                    type: 'road_edge',
                    data: {
                        roadId: road.id,
                        insertIndex: i + 1,
                        x: mouseX,
                        y: mouseY,
                        road: road
                    }
                };
            }
        }
    }

    return null;
};

function distanceToLineSegment(px, py, x1, y1, x2, y2) {
    const dx = x2 - x1;
    const dy = y2 - y1;
    const length = Math.sqrt(dx * dx + dy * dy);
    
    if (length === 0) return Math.sqrt((px - x1) ** 2 + (py - y1) ** 2);
    
    const t = Math.max(0, Math.min(1, ((px - x1) * dx + (py - y1) * dy) / (length * length)));
    const projX = x1 + t * dx;
    const projY = y1 + t * dy;
    
    return Math.sqrt((px - projX) ** 2 + (py - projY) ** 2);
}