import React, { useState, useRef, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';
import {
    drawNode,
    drawUnidirectionalEdge,
    drawBidirectionalEdge,
    drawCar,
    drawPallet,
    drawCarWithPallet
} from '../utils/canvasUtils';
import { drawRoadBackground, detectRoadClick } from '../utils/roadDrawing';

// LOD渲染阈值常量
const LOD_THRESHOLDS = {
    MINIMAL: 0.5,      // 只显示连接线，不显示箭头和节点
    MEDIUM: 1.0,       // 显示连接线和箭头，不显示节点
    FULL: 1.0          // 显示所有元素
};

// ====================================================================================
// --- FILE: src/components/CanvasRenderer.js ---
// 处理所有 Canvas 渲染和交互的专用组件。
// ====================================================================================
export const CanvasRenderer = forwardRef(({
    mapData,
    roadData,
    selectedElement,
    onElementClick,
    isAddingMode = false,
    layerSettings = {
        showRoads: true,
        showNodes: true,
        showEdges: true,
        roadOpacity: 0.8,
        nodeOpacity: 1.0,
        edgeOpacity: 1.0
    }
}, ref) => {
    const canvasRef = useRef(null);
    const containerRef = useRef(null);

    const [scale, setScale] = useState(1);
    const [offset, setOffset] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [isSpacePressed, setIsSpacePressed] = useState(false);
    const lastMousePosition = useRef({ x: 0, y: 0 });

    const MIN_SCALE = 0.000001;
    const MAX_SCALE = 5;
    const ZOOM_SENSITIVITY = 0.001;

    // --- 绘图逻辑 ---
    const draw = useCallback(() => {
        const canvas = canvasRef.current;
        if (!canvas || !mapData.nodes) return;

        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        ctx.save();
        ctx.translate(offset.x, offset.y);
        ctx.scale(scale, scale);

        // 1. 绘制道路系统（背景层，浅色显示）
        if (layerSettings.showRoads && roadData.currentScheme?.roads) {
            ctx.save();
            ctx.globalAlpha = layerSettings.roadOpacity;
            roadData.currentScheme.roads.forEach(road => {
                drawRoadBackground(ctx, road, mapData.nodes, scale);
            });
            ctx.restore();
        }

        // 2. 绘制双向边（路线连接）- 根据LOD级别决定是否显示箭头
        if (layerSettings.showEdges) {
            ctx.save();
            ctx.globalAlpha = layerSettings.edgeOpacity;
            mapData.bidirectional_edges?.forEach(edge => {
                const isSelected = selectedElement?.type === 'edge' && selectedElement?.data?.id === edge.id;
                const isHighlighted = isAddingMode; // 在新增模式下高亮显示可点击的路线
                const showArrows = scale >= LOD_THRESHOLDS.MEDIUM;
                drawBidirectionalEdge(ctx, edge, mapData.nodes, scale, isSelected, isHighlighted, showArrows);
            });
            ctx.restore();
        }

        // 3. 绘制单向边（路线连接）- 根据LOD级别决定是否显示箭头
        if (layerSettings.showEdges) {
            ctx.save();
            ctx.globalAlpha = layerSettings.edgeOpacity;
            mapData.unidirectional_edges?.forEach(edge => {
                const isSelected = selectedElement?.type === 'edge' && selectedElement?.data?.id === edge.id;
                const isHighlighted = isAddingMode; // 在新增模式下高亮显示可点击的路线
                const showArrows = scale >= LOD_THRESHOLDS.MEDIUM;
                drawUnidirectionalEdge(ctx, edge, mapData.nodes, scale, isSelected, isHighlighted, showArrows);
            });
            ctx.restore();
        }

        // 4. 绘制节点（最上层）- 只在高缩放级别显示
        if (layerSettings.showNodes && scale > LOD_THRESHOLDS.FULL) {
            ctx.save();
            ctx.globalAlpha = layerSettings.nodeOpacity;
            mapData.nodes.forEach(node => {
                const isSelected = selectedElement?.type === 'node' && selectedElement?.data?.id === node.id;
                drawNode(ctx, node, scale, isSelected);
            });
            ctx.restore();
        }

        ctx.restore();
    }, [scale, offset, mapData, roadData, selectedElement, isAddingMode, layerSettings]);

    useEffect(() => {
        draw();
    }, [draw]);

    // --- 视图控制 (缩放, 平移, 重置) ---
    const resetView = useCallback(() => {
        const { nodes } = mapData;
        const container = containerRef.current;
        if (!container || nodes.size === 0) return;

        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
        nodes.forEach(node => {
            if (node.x < minX) minX = node.x;
            if (node.y < minY) minY = node.y;
            if (node.x > maxX) maxX = node.x;
            if (node.y > maxY) maxY = node.y;
        });

        const { width: viewWidth, height: viewHeight } = container.getBoundingClientRect();
        const mapWidth = maxX - minX;
        const mapHeight = maxY - minY;
        const mapCenterX = minX + mapWidth / 2;
        const mapCenterY = minY + mapHeight / 2;

        const scaleX = mapWidth > 0 ? (viewWidth / mapWidth) * 0.9 : 1;
        const scaleY = mapHeight > 0 ? (viewHeight / mapHeight) * 0.9 : 1;
        const initialScale = Math.min(scaleX, scaleY, MAX_SCALE);

        setOffset({
            x: viewWidth / 2 - mapCenterX * initialScale,
            y: viewHeight / 2 - mapCenterY * initialScale,
        });
        setScale(initialScale);
    }, [mapData]);

    useEffect(() => {
        // 数据加载后自动重置视图
        if (mapData.nodes.size > 0) {
            resetView();
        }
    }, [mapData.nodes, resetView]);

    // 缩放控制函数
    const zoomIn = useCallback(() => {
        const newScale = Math.min(MAX_SCALE, scale * 1.05);
        setScale(newScale);
    }, [scale]);

    const zoomOut = useCallback(() => {
        const newScale = Math.max(MIN_SCALE, scale / 1.05);
        setScale(newScale);
    }, [scale]);

    // 通过ref暴露控制方法
    useImperativeHandle(ref, () => ({
        zoomIn,
        zoomOut,
        resetView
    }), [zoomIn, zoomOut, resetView]);

    // --- 事件处理器 ---
    const handleWheel = useCallback((e) => {
        e.preventDefault();
        if (!canvasRef.current) return;
        const rect = canvasRef.current.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        const zoomFactor = 1 - e.deltaY * ZOOM_SENSITIVITY;
        const newScale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale * zoomFactor));
        const worldX = (mouseX - offset.x) / scale;
        const worldY = (mouseY - offset.y) / scale;
        setOffset({ x: mouseX - worldX * newScale, y: mouseY - worldY * newScale });
        setScale(newScale);
    }, [scale, offset]);

    // 处理元素点击选择
    const handleElementClick = useCallback((event) => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const rect = canvas.getBoundingClientRect();
        const x = (event.clientX - rect.left - offset.x) / scale;
        const y = (event.clientY - rect.top - offset.y) / scale;

        let clickedElement = null;

        // 检测道路点击
        if (roadData.currentScheme?.roads) {
            clickedElement = detectRoadClick(roadData.currentScheme.roads, mapData.nodes, x, y, scale);
        }

        // 如果没有点击道路，检测节点
        if (!clickedElement) {
            // 检测节点点击
            mapData.nodes.forEach(node => {
                const distance = Math.sqrt(Math.pow(x - node.x, 2) + Math.pow(y - node.y, 2));
                const tolerance = 15 / scale;

                if (distance <= tolerance) {
                    clickedElement = {
                        type: 'node',
                        data: node
                    };
                }
            });
        }

        // 如果没有点击节点，检测边
        if (!clickedElement) {
            // 动态调整容差：缩放级别越小，容差越大
            const baseTolerance = Math.max(8, 15 / scale);
            let closestEdge = null;
            let closestDistance = Infinity;

            // 检测双向边点击
            mapData.bidirectional_edges?.forEach(edge => {
                const [node1Id, node2Id] = edge.nodes;
                const node1 = mapData.nodes.get(node1Id);
                const node2 = mapData.nodes.get(node2Id);

                if (node1 && node2) {
                    const distance = distanceToLineSegment(x, y, node1.x, node1.y, node2.x, node2.y);

                    if (distance <= baseTolerance && distance < closestDistance) {
                        closestDistance = distance;
                        closestEdge = {
                            type: 'edge',
                            data: { ...edge, x, y, edgeType: 'bidirectional' }
                        };
                    }
                }
            });

            // 检测单向边点击
            mapData.unidirectional_edges?.forEach(edge => {
                const node1 = mapData.nodes.get(edge.start_node_id);
                const node2 = mapData.nodes.get(edge.end_node_id);

                if (node1 && node2) {
                    const distance = distanceToLineSegment(x, y, node1.x, node1.y, node2.x, node2.y);

                    if (distance <= baseTolerance && distance < closestDistance) {
                        closestDistance = distance;
                        closestEdge = {
                            type: 'edge',
                            data: { ...edge, x, y, edgeType: 'unidirectional' }
                        };
                    }
                }
            });

            clickedElement = closestEdge;
        }

        onElementClick?.(clickedElement);
    }, [mapData, roadData, scale, offset, onElementClick]);

    const handleMouseDown = useCallback((e) => {
        if (isSpacePressed) {
            setIsDragging(true);
            lastMousePosition.current = { x: e.clientX, y: e.clientY };
            if (canvasRef.current) canvasRef.current.style.cursor = 'grabbing';
        } else {
            // 处理元素选择
            handleElementClick(e);
        }
    }, [isSpacePressed, handleElementClick]);

    // 计算点到线段的距离
    const distanceToLineSegment = (px, py, x1, y1, x2, y2) => {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;

        if (lenSq === 0) return Math.sqrt(A * A + B * B);

        let param = dot / lenSq;

        if (param < 0) {
            return Math.sqrt(A * A + B * B);
        } else if (param > 1) {
            const E = px - x2;
            const F = py - y2;
            return Math.sqrt(E * E + F * F);
        } else {
            const projX = x1 + param * C;
            const projY = y1 + param * D;
            const G = px - projX;
            const H = py - projY;
            return Math.sqrt(G * G + H * H);
        }
    };

    const handleMouseMove = useCallback((e) => {
        if (isDragging) {
            const dx = e.clientX - lastMousePosition.current.x;
            const dy = e.clientY - lastMousePosition.current.y;
            setOffset(prev => ({ x: prev.x + dx, y: prev.y + dy }));
            lastMousePosition.current = { x: e.clientX, y: e.clientY };
        }
    }, [isDragging]);

    const handleMouseUp = useCallback(() => {
        setIsDragging(false);
        if (canvasRef.current) canvasRef.current.style.cursor = isSpacePressed ? 'grab' : 'default';
    }, [isSpacePressed]);

    const handleKeyDown = useCallback((e) => {
        if (e.code === 'Space') {
            e.preventDefault();
            setIsSpacePressed(true);
            if (canvasRef.current && !isDragging) canvasRef.current.style.cursor = 'grab';
        }
    }, [isDragging]);

    const handleKeyUp = useCallback((e) => {
        if (e.code === 'Space') {
            setIsSpacePressed(false);
            if (canvasRef.current && !isDragging) canvasRef.current.style.cursor = 'default';
        }
    }, [isDragging]);

    // --- 事件监听器设置 ---
    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;
        container.addEventListener('wheel', handleWheel, { passive: false });
        window.addEventListener('keydown', handleKeyDown);
        window.addEventListener('keyup', handleKeyUp);
        container.addEventListener('mousedown', handleMouseDown);
        window.addEventListener('mousemove', handleMouseMove);
        window.addEventListener('mouseup', handleMouseUp);

        return () => {
            container.removeEventListener('wheel', handleWheel);
            window.removeEventListener('keydown', handleKeyDown);
            window.removeEventListener('keyup', handleKeyUp);
            container.removeEventListener('mousedown', handleMouseDown);
            window.removeEventListener('mousemove', handleMouseMove);
            window.removeEventListener('mouseup', handleMouseUp);
        };
    }, [handleWheel, handleKeyDown, handleKeyUp, handleMouseDown, handleMouseMove, handleMouseUp]);

    // Canvas 的 Resize observer
    useEffect(() => {
        const canvas = canvasRef.current;
        const container = containerRef.current;
        if (!canvas || !container) return;

        const resizeObserver = new ResizeObserver(() => {
            const { width, height } = container.getBoundingClientRect();
            const dpr = window.devicePixelRatio || 1;
            canvas.width = width * dpr;
            canvas.height = height * dpr;
            canvas.style.width = `${width}px`;
            canvas.style.height = `${height}px`;
            const ctx = canvas.getContext('2d');
            ctx.scale(dpr, dpr);
            draw();
        });

        resizeObserver.observe(container);
        return () => resizeObserver.disconnect();
    }, [draw]);

    return (
        <div ref={containerRef} className="w-full h-full bg-gray-800">
            <canvas ref={canvasRef} className="absolute top-0 left-0 w-full h-full" />
            <div className="absolute top-2 right-4 flex flex-col gap-2 z-20">
                <p className="text-xs font-mono text-amber-400 mt-1 bg-black bg-opacity-50 px-2 py-1 rounded">
                    缩放: {scale.toFixed(3)}
                </p>
            </div>
        </div>
    );
});

function findNearestNode(nodes, x, y) {
    let nearestNode = null;
    let minDistance = Infinity;

    nodes.forEach(node => {
        const distance = Math.sqrt(Math.pow(x - node.x, 2) + Math.pow(y - node.y, 2));
        if (distance < minDistance) {
            minDistance = distance;
            nearestNode = node.id;
        }
    });

    return nearestNode;
}
