// ====================================================================================
// --- FILE: src/utils/canvasUtils.js ---
// 此文件包含用于 Canvas 的所有原生绘图函数。
// ====================================================================================

export const hexToRgba = (hex, alpha) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

export const drawNode = (ctx, node, scale, isSelected) => {
    const radius = isSelected ? 8 / scale : 6 / scale;

    // 绘制外发光效果（选中时）
    if (isSelected) {
        ctx.save();
        ctx.shadowColor = '#FBBF24';
        ctx.shadowBlur = 20 / scale;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        ctx.beginPath();
        ctx.arc(node.x, node.y, radius + 4 / scale, 0, 2 * Math.PI);
        ctx.fillStyle = 'rgba(251, 191, 36, 0.3)';
        ctx.fill();
        ctx.restore();
    }

    ctx.beginPath();
    ctx.arc(node.x, node.y, radius, 0, 2 * Math.PI);
    ctx.fillStyle = isSelected ? '#FBBF24' : '#F59E0B';
    ctx.fill();
    ctx.strokeStyle = '#D97706';
    ctx.lineWidth = 1 / scale;
    ctx.stroke();
};

export const drawArrow = (ctx, fromX, fromY, toX, toY, headLength, color) => {
    const dx = toX - fromX;
    const dy = toY - fromY;
    const angle = Math.atan2(dy, dx);
    ctx.beginPath();
    ctx.moveTo(fromX, fromY);
    ctx.lineTo(toX, toY);
    ctx.strokeStyle = color;
    ctx.stroke();
    ctx.beginPath();
    ctx.moveTo(toX, toY);
    ctx.lineTo(toX - headLength * Math.cos(angle - Math.PI / 6), toY - headLength * Math.sin(angle - Math.PI / 6));
    ctx.lineTo(toX - headLength * Math.cos(angle + Math.PI / 6), toY - headLength * Math.sin(angle + Math.PI / 6));
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();
};

// 绘制小箭头（用于双向边）
const drawSmallArrow = (ctx, fromX, fromY, toX, toY, arrowSize, color) => {
    const angle = Math.atan2(toY - fromY, toX - fromX);

    // 在线段的1/4处绘制小箭头
    const arrowX = fromX + (toX - fromX) * 0.25;
    const arrowY = fromY + (toY - fromY) * 0.25;

    ctx.fillStyle = color;
    ctx.beginPath();
    ctx.moveTo(arrowX, arrowY);
    ctx.lineTo(
        arrowX - arrowSize * Math.cos(angle - Math.PI / 6),
        arrowY - arrowSize * Math.sin(angle - Math.PI / 6)
    );
    ctx.lineTo(
        arrowX - arrowSize * Math.cos(angle + Math.PI / 6),
        arrowY - arrowSize * Math.sin(angle + Math.PI / 6)
    );
    ctx.closePath();
    ctx.fill();
};

export const drawUnidirectionalEdge = (ctx, edge, nodes, scale, isSelected, isHighlighted = false, showArrows = true) => {
    // 如果传入的是节点对象而不是edge对象，使用旧的函数签名
    if (typeof edge.x !== 'undefined' && typeof nodes.x !== 'undefined') {
        return drawUnidirectionalEdgeNodes(ctx, edge, nodes, scale, isSelected);
    }

    // 新的函数签名：处理edge对象
    const startNode = nodes.get(edge.start_node_id);
    const endNode = nodes.get(edge.end_node_id);

    if (!startNode || !endNode) return;

    let color = '#3B82F6';
    if (isSelected) color = '#60A5FA';
    else if (isHighlighted) color = '#10B981'; // 绿色表示可点击

    // 绘制外发光效果（选中或高亮时）
    if (isSelected || isHighlighted) {
        ctx.save();
        ctx.shadowColor = isSelected ? '#60A5FA' : '#10B981';
        ctx.shadowBlur = 15 / scale;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        ctx.lineWidth = 8 / scale;
        ctx.strokeStyle = isSelected ? 'rgba(96, 165, 250, 0.4)' : 'rgba(16, 185, 129, 0.4)';
        ctx.beginPath();
        ctx.moveTo(startNode.x, startNode.y);
        ctx.lineTo(endNode.x, endNode.y);
        ctx.stroke();
        ctx.restore();
    }

    ctx.lineWidth = (isSelected || isHighlighted ? 4 : 2) / scale;

    if (showArrows) {
        // 绘制带箭头的线
        drawArrow(ctx, startNode.x, startNode.y, endNode.x, endNode.y, 15 / scale, color);
    } else {
        // 只绘制简单的线条
        ctx.strokeStyle = color;
        ctx.beginPath();
        ctx.moveTo(startNode.x, startNode.y);
        ctx.lineTo(endNode.x, endNode.y);
        ctx.stroke();
    }
};

// 保持向后兼容的函数
export const drawUnidirectionalEdgeNodes = (ctx, startNode, endNode, scale, isSelected) => {
    const color = isSelected ? '#60A5FA' : '#3B82F6';

    // 绘制外发光效果（选中时）
    if (isSelected) {
        ctx.save();
        ctx.shadowColor = '#60A5FA';
        ctx.shadowBlur = 15 / scale;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        ctx.lineWidth = 8 / scale;
        ctx.strokeStyle = 'rgba(96, 165, 250, 0.4)';
        ctx.beginPath();
        ctx.moveTo(startNode.x, startNode.y);
        ctx.lineTo(endNode.x, endNode.y);
        ctx.stroke();
        ctx.restore();
    }

    ctx.lineWidth = (isSelected ? 4 : 2) / scale;
    drawArrow(ctx, startNode.x, startNode.y, endNode.x, endNode.y, 15 / scale, color);
};

export const drawBidirectionalEdge = (ctx, edge, nodes, scale, isSelected, isHighlighted = false, showArrows = true) => {
    // 如果传入的是节点对象而不是edge对象，使用旧的函数签名
    if (typeof edge.x !== 'undefined' && typeof nodes.x !== 'undefined') {
        return drawBidirectionalEdgeNodes(ctx, edge, nodes, scale, isSelected);
    }

    // 新的函数签名：处理edge对象
    const [node1Id, node2Id] = edge.nodes;
    const startNode = nodes.get(node1Id);
    const endNode = nodes.get(node2Id);

    if (!startNode || !endNode) return;

    let color = '#8B5CF6';
    if (isSelected) color = '#A78BFA';
    else if (isHighlighted) color = '#10B981'; // 绿色表示可点击

    // 绘制外发光效果（选中或高亮时）
    if (isSelected || isHighlighted) {
        ctx.save();
        ctx.shadowColor = isSelected ? '#A78BFA' : '#10B981';
        ctx.shadowBlur = 15 / scale;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        ctx.lineWidth = 10 / scale;
        ctx.strokeStyle = isSelected ? 'rgba(167, 139, 250, 0.4)' : 'rgba(16, 185, 129, 0.4)';
        ctx.beginPath();
        ctx.moveTo(startNode.x, startNode.y);
        ctx.lineTo(endNode.x, endNode.y);
        ctx.stroke();
        ctx.restore();
    }

    ctx.lineWidth = (isSelected || isHighlighted ? 6 : 4) / scale;
    ctx.strokeStyle = color;
    ctx.beginPath();
    ctx.moveTo(startNode.x, startNode.y);
    ctx.lineTo(endNode.x, endNode.y);
    ctx.stroke();

    // 如果需要显示箭头，在两端绘制小箭头表示双向
    if (showArrows) {
        const arrowSize = 8 / scale;
        // 绘制两个小箭头表示双向
        drawSmallArrow(ctx, startNode.x, startNode.y, endNode.x, endNode.y, arrowSize, color);
        drawSmallArrow(ctx, endNode.x, endNode.y, startNode.x, startNode.y, arrowSize, color);
    }
};

// 保持向后兼容的函数
export const drawBidirectionalEdgeNodes = (ctx, startNode, endNode, scale, isSelected) => {
    const color = isSelected ? '#A78BFA' : '#8B5CF6';

    // 绘制外发光效果（选中时）
    if (isSelected) {
        ctx.save();
        ctx.shadowColor = '#A78BFA';
        ctx.shadowBlur = 15 / scale;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        ctx.lineWidth = 10 / scale;
        ctx.strokeStyle = 'rgba(167, 139, 250, 0.4)';
        ctx.beginPath();
        ctx.moveTo(startNode.x, startNode.y);
        ctx.lineTo(endNode.x, endNode.y);
        ctx.stroke();
        ctx.restore();
    }

    ctx.lineWidth = (isSelected ? 6 : 4) / scale;
    ctx.strokeStyle = color;
    ctx.beginPath();
    ctx.moveTo(startNode.x, startNode.y);
    ctx.lineTo(endNode.x, endNode.y);
    ctx.stroke();
};

export const drawCar = (ctx, car, scale, isSelected) => {
    const { x, y, angle } = car.position;
    const carWidth = 12 / scale;
    const carLength = 20 / scale;

    ctx.save();
    ctx.translate(x, y);
    ctx.rotate(angle);

    // 绘制外发光效果（选中时）
    if (isSelected) {
        ctx.save();
        ctx.shadowColor = '#DC2626';
        ctx.shadowBlur = 20 / scale;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        // 外发光轮廓
        ctx.fillStyle = 'rgba(220, 38, 38, 0.3)';
        ctx.beginPath();
        ctx.moveTo(-carWidth / 2 - 4 / scale, carLength / 2 + 4 / scale);
        ctx.lineTo(carWidth / 2 + 4 / scale, carLength / 2 + 4 / scale);
        ctx.lineTo(carWidth * 0.8 + 4 / scale, -carLength / 2 - 4 / scale);
        ctx.lineTo(-carWidth * 0.8 - 4 / scale, -carLength / 2 - 4 / scale);
        ctx.closePath();
        ctx.fill();
        ctx.restore();
    }

    // 车身
    ctx.fillStyle = isSelected ? '#EF4444' : '#DC2626'; // Brighter red when selected
    ctx.beginPath();
    ctx.moveTo(-carWidth / 2, carLength / 2);
    ctx.lineTo(carWidth / 2, carLength / 2);
    ctx.lineTo(carWidth * 0.8, -carLength / 2);
    ctx.lineTo(-carWidth * 0.8, -carLength / 2);
    ctx.closePath();
    ctx.fill();

    // 挡风玻璃
    ctx.fillStyle = '#60A5FA'; // A light blue
    ctx.beginPath();
    ctx.moveTo(-carWidth * 0.6, -carLength * 0.1);
    ctx.lineTo(carWidth * 0.6, -carLength * 0.1);
    ctx.lineTo(carWidth * 0.7, -carLength / 2);
    ctx.lineTo(-carWidth * 0.7, -carLength / 2);
    ctx.closePath();
    ctx.fill();

    // 车头灯
    ctx.fillStyle = '#FBBF24'; // Yellow
    ctx.beginPath();
    ctx.arc(-carWidth * 0.5, -carLength/2, 2 / scale, 0, 2* Math.PI);
    ctx.arc(carWidth * 0.5, -carLength/2, 2 / scale, 0, 2* Math.PI);
    ctx.fill();

    ctx.restore();
};

export const drawPallet = (ctx, pallet, scale, isSelected) => {
    const { x, y } = pallet.position;
    // 托盘的不同形状
    const shape = pallet.shape || 'standard';
    const width = (shape === 'large' ? 30 : 20) / scale;
    const height = (shape === 'long' ? 30 : 20) / scale;

    ctx.save();
    ctx.translate(x, y);

    // 绘制外发光效果（选中时）
    if (isSelected) {
        ctx.save();
        ctx.shadowColor = '#A16207';
        ctx.shadowBlur = 20 / scale;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        // 外发光轮廓
        ctx.fillStyle = 'rgba(161, 98, 7, 0.3)';
        ctx.fillRect(-width / 2 - 4 / scale, -height / 2 - 4 / scale,
                    width + 8 / scale, height + 8 / scale);
        ctx.restore();
    }

    ctx.fillStyle = isSelected ? '#D97706' : '#A16207'; // Brighter brown when selected
    ctx.strokeStyle = '#422006';
    ctx.lineWidth = 1 / scale;
    ctx.fillRect(-width / 2, -height / 2, width, height);
    ctx.strokeRect(-width / 2, -height / 2, width, height);
    ctx.restore();
};

// 绘制小车带托盘的组合状态
export const drawCarWithPallet = (ctx, car, pallet, scale, isSelected) => {
    const { x, y, angle } = car.position;
    const carWidth = 12 / scale;
    const carLength = 20 / scale;

    // 托盘尺寸
    const palletShape = pallet?.shape || 'standard';
    const palletWidth = (palletShape === 'large' ? 30 : 20) / scale;
    const palletHeight = (palletShape === 'long' ? 30 : 20) / scale;

    ctx.save();
    ctx.translate(x, y);
    ctx.rotate(angle);

    // 绘制外发光效果（选中时）
    if (isSelected) {
        ctx.save();
        ctx.shadowColor = '#DC2626';
        ctx.shadowBlur = 25 / scale;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        // 外发光轮廓 - 包含托盘区域
        ctx.fillStyle = 'rgba(220, 38, 38, 0.3)';
        ctx.beginPath();
        ctx.moveTo(-Math.max(carWidth, palletWidth) / 2 - 4 / scale, carLength / 2 + 4 / scale);
        ctx.lineTo(Math.max(carWidth, palletWidth) / 2 + 4 / scale, carLength / 2 + 4 / scale);
        ctx.lineTo(carWidth * 0.8 + 4 / scale, -carLength / 2 - 4 / scale);
        ctx.lineTo(-carWidth * 0.8 - 4 / scale, -carLength / 2 - 4 / scale);
        ctx.closePath();
        ctx.fill();
        ctx.restore();
    }

    // 1. 先绘制小车车身（底层，完整显示）
    ctx.fillStyle = isSelected ? '#EF4444' : '#DC2626';
    ctx.beginPath();
    ctx.moveTo(-carWidth / 2, carLength / 2);
    ctx.lineTo(carWidth / 2, carLength / 2);
    ctx.lineTo(carWidth * 0.8, -carLength / 2);
    ctx.lineTo(-carWidth * 0.8, -carLength / 2);
    ctx.closePath();
    ctx.fill();

    // 挡风玻璃
    ctx.fillStyle = '#60A5FA';
    ctx.beginPath();
    ctx.moveTo(-carWidth * 0.6, -carLength * 0.1);
    ctx.lineTo(carWidth * 0.6, -carLength * 0.1);
    ctx.lineTo(carWidth * 0.7, -carLength / 2);
    ctx.lineTo(-carWidth * 0.7, -carLength / 2);
    ctx.closePath();
    ctx.fill();

    // 车头灯
    ctx.fillStyle = '#FBBF24';
    ctx.beginPath();
    ctx.arc(-carWidth * 0.5, -carLength/2, 2 / scale, 0, 2* Math.PI);
    ctx.arc(carWidth * 0.5, -carLength/2, 2 / scale, 0, 2* Math.PI);
    ctx.fill();

    // 2. 如果有托盘，在小车上方绘制半透明托盘
    if (pallet) {
        // 托盘颜色（半透明）
        const palletColor = isSelected ? 'rgba(217, 119, 6, 0.6)' : 'rgba(161, 98, 7, 0.6)';
        const palletBorderColor = isSelected ? '#D97706' : '#A16207';

        // 绘制半透明托盘
        ctx.fillStyle = palletColor;
        ctx.strokeStyle = palletBorderColor;
        ctx.lineWidth = 1 / scale;

        ctx.fillRect(-palletWidth / 2, -palletHeight / 2, palletWidth, palletHeight);
        ctx.strokeRect(-palletWidth / 2, -palletHeight / 2, palletWidth, palletHeight);

        // 绘制载货指示器
        ctx.fillStyle = '#10B981';
        ctx.beginPath();
        ctx.arc(palletWidth * 0.3, -palletHeight * 0.3, 2 / scale, 0, 2 * Math.PI);
        ctx.fill();
    }

    ctx.restore();
};