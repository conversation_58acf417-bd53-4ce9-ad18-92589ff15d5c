import React, { useState, useRef } from 'react';
import { Navigation } from './components/Navigation';
import { DetailPanel } from './components/DetailPanel';
import { CanvasRenderer } from './components/CanvasRenderer';
import { Timeline } from './components/Timeline';
import { SchemeSelector } from './components/SchemeSelector';
import { useMapData } from './hooks/useMapData';
import { useSimulation } from './hooks/useSimulation';
import { useDynamicState } from './hooks/useDynamicState';
import { useRoadData } from './hooks/useRoadData';

// ====================================================================================
// --- FILE: src/App.js ---
// 这是主应用组件。它协调所有其他组件和状态。
// ====================================================================================
function FactoryCanvasApp() {
    // 方案选择状态
    const [selectedScheme, setSelectedScheme] = useState(null);

    // 只有选择了方案才加载数据
    const { mapData, isLoading: mapLoading } = useMapData(selectedScheme?.mapDataUrl || null);
    const { roadData, isLoading: roadLoading, setCurrentScheme, addPointToRoad, removePointFromRoad } = useRoadData(selectedScheme?.roadDataUrl || null);
    const {
        currentTime,
        isPlaying,
        play,
        pause,
        setTime,
        setRoute,
        simulationData
    } = useSimulation(mapData);

    // 使用新的动态状态管理
    const {
        dynamicState,
        dynamicElements,
        updateDynamicStateFromJson,
        updateCarState,
        updatePalletState
    } = useDynamicState(mapData, simulationData, currentTime);

    // 选定元素的状态
    const [selectedElement, setSelectedElement] = useState(null);

    // 控制是否显示动态元素（默认显示）
    const [showDynamicElements, setShowDynamicElements] = useState(true);

    // 视图控制引用 - 用于从外部控制CanvasRenderer的视图
    const canvasControlsRef = useRef(null);

    const handleSetRoute = (startNodeId, endNodeId) => {
        setRoute(startNodeId, endNodeId);
    };

    const handleRoadPointAdd = (roadId, pointId, insertIndex) => {
        addPointToRoad(roadId, pointId, insertIndex);
    };

    const handleRoadPointRemove = (roadId, pointIndex) => {
        removePointFromRoad(roadId, pointIndex);
    };

    // 视图控制函数
    const handleZoomIn = () => {
        if (canvasControlsRef.current?.zoomIn) {
            canvasControlsRef.current.zoomIn();
        }
    };

    const handleZoomOut = () => {
        if (canvasControlsRef.current?.zoomOut) {
            canvasControlsRef.current.zoomOut();
        }
    };

    const handleResetView = () => {
        if (canvasControlsRef.current?.resetView) {
            canvasControlsRef.current.resetView();
        }
    };

    // 处理方案选择
    const handleSchemeSelect = (scheme) => {
        setSelectedScheme(scheme);
    };

    // 返回方案选择界面
    const handleBackToSchemes = () => {
        setSelectedScheme(null);
        setSelectedElement(null);
    };

    // 如果没有选择方案，显示方案选择界面
    if (!selectedScheme) {
        return <SchemeSelector onSchemeSelect={handleSchemeSelect} />;
    }

    // 如果正在加载数据，显示加载界面
    if (mapLoading || roadLoading) {
        return (
            <div className="h-screen flex items-center justify-center bg-gray-100">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">正在加载方案数据...</p>
                    <p className="text-sm text-gray-500 mt-2">{selectedScheme.name}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="w-full h-full font-sans flex overflow-hidden bg-gray-800">
            {/* 左侧导航栏 */}
            <Navigation
                mapData={mapData}
                roadData={roadData}
                selectedScheme={selectedScheme}
                onSetRoute={handleSetRoute}
                onSchemeChange={setCurrentScheme}
                onBackToSchemes={handleBackToSchemes}
                onZoomIn={handleZoomIn}
                onZoomOut={handleZoomOut}
                onResetView={handleResetView}
                selectedElement={selectedElement}
            />

            {/* 中间地图区域 */}
            <main className="flex-grow relative">
                <CanvasRenderer
                    ref={canvasControlsRef}
                    mapData={mapData}
                    roadData={roadData}
                    dynamicElements={dynamicElements}
                    selectedElement={selectedElement}
                    onElementClick={setSelectedElement}
                    onRoadPointAdd={handleRoadPointAdd}
                    onRoadPointRemove={handleRoadPointRemove}
                    showDynamicElements={showDynamicElements}
                />

                {/* 时间轴覆盖在地图底部 */}
                <Timeline
                    currentTime={currentTime}
                    maxTime={simulationData.route?.totalTime || 0}
                    isPlaying={isPlaying}
                    onPlay={play}
                    onPause={pause}
                    onTimeChange={setTime}
                />
            </main>

            {/* 右侧详情面板 */}
            <DetailPanel
                selectedElement={selectedElement}
                simulationData={simulationData}
            />
        </div>
    );
}


// --- 应用的主导出 ---
export default function App() {
    return (
        <div style={{ width: '100vw', height: '100vh' }}>
            <FactoryCanvasApp />
        </div>
    );
}

