import { useState, useEffect } from 'react';

export const useRoadData = (url) => {
    const [roadData, setRoadData] = useState({
        schemes: [],
        currentScheme: null
    });
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const loadRoadData = async () => {
            setIsLoading(true);
            try {
                const response = await fetch(url);
                if (!response.ok) throw new Error(`无法加载道路数据: ${response.statusText}`);
                const data = await response.json();

                setRoadData({
                    schemes: data.schemes || [],
                    currentScheme: data.schemes?.[0] || null
                });
            } catch (error) {
                console.error("解析道路JSON时出错:", error);
                setRoadData({
                    schemes: [],
                    currentScheme: null
                });
            } finally {
                setIsLoading(false);
            }
        };
        loadRoadData();
    }, [url]);

    const setCurrentScheme = (schemeId) => {
        const scheme = roadData.schemes.find(s => s.id === schemeId);
        setRoadData(prev => ({
            ...prev,
            currentScheme: scheme || null
        }));
    };

    const addPointToRoad = (roadId, pointId, insertIndex) => {
        if (!roadData.currentScheme) return;

        setRoadData(prev => ({
            ...prev,
            currentScheme: {
                ...prev.currentScheme,
                roads: prev.currentScheme.roads.map(road => {
                    if (road.id === roadId) {
                        const newPoints = [...road.points];
                        newPoints.splice(insertIndex, 0, pointId);
                        return { ...road, points: newPoints };
                    }
                    return road;
                })
            }
        }));
    };

    const removePointFromRoad = (roadId, pointIndex) => {
        if (!roadData.currentScheme) return;

        setRoadData(prev => ({
            ...prev,
            currentScheme: {
                ...prev.currentScheme,
                roads: prev.currentScheme.roads.map(road => {
                    if (road.id === roadId && road.points.length > 2) {
                        const newPoints = [...road.points];
                        newPoints.splice(pointIndex, 1);
                        return { ...road, points: newPoints };
                    }
                    return road;
                })
            }
        }));
    };

    return {
        roadData,
        isLoading,
        setCurrentScheme,
        addPointToRoad,
        removePointFromRoad
    };
};