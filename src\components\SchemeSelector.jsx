import { useState } from 'react';
import { SchemeManager } from './SchemeManager';

// 模拟的方案数据
const INITIAL_SCHEMES = [
    {
        id: 'scheme-1',
        name: '工厂布局方案 A',
        description: '标准生产线布局，适用于中小型制造',
        mapDataUrl: '/map_data.json',
        roadDataUrl: '/road_data.json',
        thumbnail: '/thumbnails/scheme-a.png',
        createdAt: '2024-01-15',
        status: 'active'
    },
    {
        id: 'scheme-2', 
        name: '工厂布局方案 B',
        description: '高效流水线布局，适用于大批量生产',
        mapDataUrl: '/map_data_b.json',
        roadDataUrl: '/road_data_b.json',
        thumbnail: '/thumbnails/scheme-b.png',
        createdAt: '2024-01-20',
        status: 'draft'
    },
    {
        id: 'scheme-3',
        name: '工厂布局方案 C', 
        description: '柔性制造布局，适用于多品种小批量',
        mapDataUrl: '/map_data_c.json',
        roadDataUrl: '/road_data_c.json',
        thumbnail: '/thumbnails/scheme-c.png',
        createdAt: '2024-01-25',
        status: 'active'
    }
];

export const SchemeSelector = ({ onSchemeSelect }) => {
    const [schemes, setSchemes] = useState(INITIAL_SCHEMES);
    const [selectedScheme, setSelectedScheme] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [showManager, setShowManager] = useState(false);

    const filteredSchemes = schemes.filter(scheme =>
        scheme.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        scheme.description.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // 方案管理函数
    const handleSchemeCreate = (newScheme) => {
        setSchemes(prev => [...prev, newScheme]);
    };

    const handleSchemeUpdate = (schemeId, updates) => {
        setSchemes(prev => prev.map(scheme =>
            scheme.id === schemeId ? { ...scheme, ...updates } : scheme
        ));
    };

    const handleSchemeDelete = (schemeId) => {
        setSchemes(prev => prev.filter(scheme => scheme.id !== schemeId));
        if (selectedScheme?.id === schemeId) {
            setSelectedScheme(null);
        }
    };

    const handleSchemeClick = (scheme) => {
        setSelectedScheme(scheme);
    };

    const handleEnterScheme = () => {
        if (selectedScheme) {
            onSchemeSelect(selectedScheme);
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'draft': return 'bg-yellow-100 text-yellow-800';
            case 'archived': return 'bg-gray-100 text-gray-800';
            default: return 'bg-blue-100 text-blue-800';
        }
    };

    const getStatusText = (status) => {
        switch (status) {
            case 'active': return '已激活';
            case 'draft': return '草稿';
            case 'archived': return '已归档';
            default: return '未知';
        }
    };

    return (
        <div className="min-h-screen bg-gray-50 p-6">
            <div className="max-w-6xl mx-auto">
                {/* 头部 */}
                <div className="mb-8">
                    <div className="flex justify-between items-center mb-4">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                工厂数字孪生系统
                            </h1>
                            <p className="text-gray-600">
                                {showManager ? '管理您的工厂布局方案' : '选择一个方案开始您的数字化工厂管理'}
                            </p>
                        </div>
                        <button
                            onClick={() => setShowManager(!showManager)}
                            className={`px-4 py-2 rounded-lg transition-colors ${
                                showManager
                                    ? 'bg-gray-600 text-white hover:bg-gray-700'
                                    : 'bg-blue-600 text-white hover:bg-blue-700'
                            }`}
                        >
                            {showManager ? '返回选择' : '管理方案'}
                        </button>
                    </div>
                </div>

                {/* 管理界面 */}
                {showManager ? (
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <SchemeManager
                            schemes={schemes}
                            onSchemeCreate={handleSchemeCreate}
                            onSchemeUpdate={handleSchemeUpdate}
                            onSchemeDelete={handleSchemeDelete}
                        />
                    </div>
                ) : (
                    <>
                        {/* 搜索栏 */}
                        <div className="mb-6">
                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder="搜索方案..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        {/* 方案网格 */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    {filteredSchemes.map((scheme) => (
                        <div
                            key={scheme.id}
                            onClick={() => handleSchemeClick(scheme)}
                            className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer border-2 ${
                                selectedScheme?.id === scheme.id 
                                    ? 'border-blue-500 ring-2 ring-blue-200' 
                                    : 'border-gray-200 hover:border-gray-300'
                            }`}
                        >
                            {/* 缩略图区域 */}
                            <div className="h-48 bg-gray-100 rounded-t-lg flex items-center justify-center">
                                <div className="text-gray-400">
                                    <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                </div>
                            </div>

                            {/* 内容区域 */}
                            <div className="p-4">
                                <div className="flex items-center justify-between mb-2">
                                    <h3 className="text-lg font-semibold text-gray-900">
                                        {scheme.name}
                                    </h3>
                                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(scheme.status)}`}>
                                        {getStatusText(scheme.status)}
                                    </span>
                                </div>
                                
                                <p className="text-gray-600 text-sm mb-3">
                                    {scheme.description}
                                </p>
                                
                                <div className="text-xs text-gray-500">
                                    创建时间: {scheme.createdAt}
                                </div>
                            </div>
                            </div>
                        ))}
                        </div>

                        {/* 操作按钮 */}
                        {selectedScheme && (
                    <div className="fixed bottom-6 right-6">
                        <div className="bg-white rounded-lg shadow-lg p-4 border">
                            <div className="mb-3">
                                <h4 className="font-medium text-gray-900">已选择方案:</h4>
                                <p className="text-sm text-gray-600">{selectedScheme.name}</p>
                            </div>
                            <div className="flex space-x-3">
                                <button
                                    onClick={() => setSelectedScheme(null)}
                                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                                >
                                    取消
                                </button>
                                <button
                                    onClick={handleEnterScheme}
                                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                                >
                                    进入方案
                                </button>
                            </div>
                            </div>
                        </div>
                        )}

                        {/* 空状态 */}
                        {filteredSchemes.length === 0 && (
                    <div className="text-center py-12">
                        <div className="text-gray-400 mb-4">
                            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的方案</h3>
                            <p className="text-gray-600">请尝试其他搜索关键词</p>
                        </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
};
