import { findPath } from '../pathfinding';

describe('pathfinding', () => {
    const mockMapData = {
        nodes: new Map([
            ['A1', { id: 'A1', x: 100, y: 100, type: 'storage' }],
            ['A2', { id: 'A2', x: 300, y: 100, type: 'storage' }],
            ['B1', { id: 'B1', x: 100, y: 200, type: 'workstation' }],
            ['B2', { id: 'B2', x: 300, y: 200, type: 'workstation' }],
            ['C1', { id: 'C1', x: 200, y: 150, type: 'junction' }]
        ]),
        unidirectional_edges: [
            {
                id: 'edge_A1_C1',
                start_node_id: 'A1',
                end_node_id: 'C1',
                travel_time: 5.0
            },
            {
                id: 'edge_C1_B1',
                start_node_id: 'C1',
                end_node_id: 'B1',
                travel_time: 5.0
            }
        ],
        bidirectional_edges: [
            {
                id: 'path_A2_C1',
                nodes: ['A2', 'C1'],
                travel_time: 5.0
            },
            {
                id: 'path_C1_B2',
                nodes: ['C1', 'B2'],
                travel_time: 5.0
            }
        ]
    };

    test('should find path with edges', () => {
        const result = findPath(mockMapData, 'A1', 'B1');
        
        expect(result).not.toBeNull();
        expect(result.steps).toHaveLength(3);
        expect(result.steps[0].nodeId).toBe('A1');
        expect(result.steps[1].nodeId).toBe('C1');
        expect(result.steps[2].nodeId).toBe('B1');
        expect(result.totalTime).toBe(10);
    });

    test('should find path using bidirectional edges', () => {
        const result = findPath(mockMapData, 'A2', 'B2');
        
        expect(result).not.toBeNull();
        expect(result.steps).toHaveLength(3);
        expect(result.steps[0].nodeId).toBe('A2');
        expect(result.steps[1].nodeId).toBe('C1');
        expect(result.steps[2].nodeId).toBe('B2');
    });

    test('should return null for invalid nodes', () => {
        const result = findPath(mockMapData, 'INVALID', 'B1');
        expect(result).toBeNull();
    });

    test('should return null for empty map data', () => {
        const result = findPath(null, 'A1', 'B1');
        expect(result).toBeNull();
    });

    test('should handle map data without edges', () => {
        const simpleMapData = {
            nodes: new Map([
                ['A', { id: 'A', x: 0, y: 0 }],
                ['B', { id: 'B', x: 100, y: 0 }],
                ['C', { id: 'C', x: 200, y: 0 }]
            ]),
            unidirectional_edges: [],
            bidirectional_edges: []
        };

        const result = findPath(simpleMapData, 'A', 'C');
        
        expect(result).not.toBeNull();
        expect(result.steps).toHaveLength(3);
        expect(result.steps[0].nodeId).toBe('A');
        expect(result.steps[2].nodeId).toBe('C');
    });

    test('should calculate correct timing for path steps', () => {
        const result = findPath(mockMapData, 'A1', 'B1');
        
        expect(result.steps[0].time).toBe(0);
        expect(result.steps[1].time).toBe(5);
        expect(result.steps[2].time).toBe(10);
    });
});
