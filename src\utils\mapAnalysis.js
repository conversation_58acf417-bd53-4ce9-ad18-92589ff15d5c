// 地图数据分析工具

/**
 * 检测孤立节点（没有任何连接的节点）
 * @param {Map} nodes - 节点Map
 * @param {Array} bidirectionalEdges - 双向边数组
 * @param {Array} unidirectionalEdges - 单向边数组
 * @returns {Array} 孤立节点数组
 */
export const findIsolatedNodes = (nodes, bidirectionalEdges = [], unidirectionalEdges = []) => {
    const connectedNodeIds = new Set();
    
    // 收集所有连接的节点ID
    bidirectionalEdges.forEach(edge => {
        if (edge.nodes && edge.nodes.length >= 2) {
            edge.nodes.forEach(nodeId => connectedNodeIds.add(nodeId));
        }
    });
    
    unidirectionalEdges.forEach(edge => {
        if (edge.start_node_id) connectedNodeIds.add(edge.start_node_id);
        if (edge.end_node_id) connectedNodeIds.add(edge.end_node_id);
    });
    
    // 找出没有连接的节点
    const isolatedNodes = [];
    nodes.forEach((node, nodeId) => {
        if (!connectedNodeIds.has(nodeId)) {
            isolatedNodes.push({
                id: nodeId,
                node: node,
                type: 'isolated_node',
                description: '孤立节点：没有任何连接'
            });
        }
    });
    
    return isolatedNodes;
};

/**
 * 使用深度优先搜索找到连通分量
 * @param {Map} nodes - 节点Map
 * @param {Array} bidirectionalEdges - 双向边数组
 * @param {Array} unidirectionalEdges - 单向边数组
 * @returns {Array} 连通分量数组
 */
export const findConnectedComponents = (nodes, bidirectionalEdges = [], unidirectionalEdges = []) => {
    // 构建邻接表
    const adjacencyList = new Map();
    
    // 初始化邻接表
    nodes.forEach((node, nodeId) => {
        adjacencyList.set(nodeId, new Set());
    });
    
    // 添加双向边
    bidirectionalEdges.forEach(edge => {
        if (edge.nodes && edge.nodes.length >= 2) {
            const [node1, node2] = edge.nodes;
            if (adjacencyList.has(node1) && adjacencyList.has(node2)) {
                adjacencyList.get(node1).add(node2);
                adjacencyList.get(node2).add(node1);
            }
        }
    });
    
    // 添加单向边（作为双向处理，用于连通性分析）
    unidirectionalEdges.forEach(edge => {
        const { start_node_id, end_node_id } = edge;
        if (adjacencyList.has(start_node_id) && adjacencyList.has(end_node_id)) {
            adjacencyList.get(start_node_id).add(end_node_id);
            adjacencyList.get(end_node_id).add(start_node_id);
        }
    });
    
    const visited = new Set();
    const components = [];
    
    // DFS函数
    const dfs = (nodeId, component) => {
        visited.add(nodeId);
        component.push(nodeId);
        
        const neighbors = adjacencyList.get(nodeId) || new Set();
        neighbors.forEach(neighborId => {
            if (!visited.has(neighborId)) {
                dfs(neighborId, component);
            }
        });
    };
    
    // 找到所有连通分量
    nodes.forEach((node, nodeId) => {
        if (!visited.has(nodeId)) {
            const component = [];
            dfs(nodeId, component);
            components.push(component);
        }
    });
    
    return components;
};

/**
 * 检测孤立岛屿（与主图不连通的节点群）
 * @param {Map} nodes - 节点Map
 * @param {Array} bidirectionalEdges - 双向边数组
 * @param {Array} unidirectionalEdges - 单向边数组
 * @returns {Array} 孤立岛屿数组
 */
export const findIsolatedIslands = (nodes, bidirectionalEdges = [], unidirectionalEdges = []) => {
    const components = findConnectedComponents(nodes, bidirectionalEdges, unidirectionalEdges);
    
    if (components.length <= 1) {
        return []; // 没有孤立岛屿
    }
    
    // 找到最大的连通分量（主图）
    const mainComponent = components.reduce((largest, current) => 
        current.length > largest.length ? current : largest
    );
    
    // 其他分量都是孤立岛屿
    const isolatedIslands = components
        .filter(component => component !== mainComponent)
        .map((component, index) => ({
            id: `island-${index}`,
            nodeIds: component,
            nodes: component.map(nodeId => ({ id: nodeId, ...nodes.get(nodeId) })),
            type: 'isolated_island',
            description: `孤立岛屿：包含 ${component.length} 个节点，与主图不连通`
        }));
    
    return isolatedIslands;
};

/**
 * 检测死胡同节点（只有一个连接的节点）
 * @param {Map} nodes - 节点Map
 * @param {Array} bidirectionalEdges - 双向边数组
 * @param {Array} unidirectionalEdges - 单向边数组
 * @returns {Array} 死胡同节点数组
 */
export const findDeadEndNodes = (nodes, bidirectionalEdges = [], unidirectionalEdges = []) => {
    const nodeConnections = new Map();
    
    // 初始化连接计数
    nodes.forEach((node, nodeId) => {
        nodeConnections.set(nodeId, 0);
    });
    
    // 计算双向边连接
    bidirectionalEdges.forEach(edge => {
        if (edge.nodes && edge.nodes.length >= 2) {
            edge.nodes.forEach(nodeId => {
                if (nodeConnections.has(nodeId)) {
                    nodeConnections.set(nodeId, nodeConnections.get(nodeId) + 1);
                }
            });
        }
    });
    
    // 计算单向边连接
    unidirectionalEdges.forEach(edge => {
        const { start_node_id, end_node_id } = edge;
        if (nodeConnections.has(start_node_id)) {
            nodeConnections.set(start_node_id, nodeConnections.get(start_node_id) + 1);
        }
        if (nodeConnections.has(end_node_id)) {
            nodeConnections.set(end_node_id, nodeConnections.get(end_node_id) + 1);
        }
    });
    
    // 找出只有一个连接的节点
    const deadEndNodes = [];
    nodeConnections.forEach((connectionCount, nodeId) => {
        if (connectionCount === 1) {
            deadEndNodes.push({
                id: nodeId,
                node: nodes.get(nodeId),
                type: 'dead_end_node',
                description: '死胡同节点：只有一个连接'
            });
        }
    });
    
    return deadEndNodes;
};

/**
 * 综合分析地图数据，返回所有异常
 * @param {Object} mapData - 地图数据
 * @returns {Object} 分析结果
 */
export const analyzeMapData = (mapData) => {
    if (!mapData || !mapData.nodes) {
        return {
            isolatedNodes: [],
            isolatedIslands: [],
            deadEndNodes: [],
            summary: {
                totalNodes: 0,
                totalEdges: 0,
                issueCount: 0
            }
        };
    }
    
    const { nodes, bidirectional_edges = [], unidirectional_edges = [] } = mapData;
    
    const isolatedNodes = findIsolatedNodes(nodes, bidirectional_edges, unidirectional_edges);
    const isolatedIslands = findIsolatedIslands(nodes, bidirectional_edges, unidirectional_edges);
    const deadEndNodes = findDeadEndNodes(nodes, bidirectional_edges, unidirectional_edges);
    
    const issueCount = isolatedNodes.length + isolatedIslands.length + deadEndNodes.length;
    
    return {
        isolatedNodes,
        isolatedIslands,
        deadEndNodes,
        summary: {
            totalNodes: nodes.size,
            totalEdges: bidirectional_edges.length + unidirectional_edges.length,
            issueCount,
            hasIssues: issueCount > 0
        }
    };
};
