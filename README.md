
### React 项目代码总结与完整代码

**本文档旨在详细解析您的工厂孪生可视化项目的架构、运行逻辑和功能实现，并提供每个模块的完整代码。**

### 1. 项目运行逻辑总结

#### 核心设计思想：关注点分离 (Separation of Concerns)

**整个应用遵循现代 React 的最佳实践，将代码精心拆分为三个逻辑部分：**

1. **`<span class="selected">components</span>` (UI 组件)** **: 只负责界面的展示和响应用户的初步操作（如点击）。它们是“哑”组件，不包含复杂的业务逻辑。**

* **文件** **: **`<span class="selected">Accordion.jsx</span>`, `<span class="selected">Sidebar.jsx</span>`, `<span class="selected">Timeline.jsx</span>`, `<span class="selected">CanvasRenderer.jsx</span>`, `<span class="selected">App.jsx</span>`

1. **`<span class="selected">hooks</span>` (逻辑钩子)** **: 封装了所有复杂、有状态的业务逻辑。组件通过调用这些 Hooks 来获得数据和控制能力，从而使组件本身保持简洁。**

* **文件** **: **`<span class="selected">useMapData.js</span>`, `<span class="selected">useSimulation.js</span>`

1. **`<span class="selected">utils</span>` (工具函数)** **: 包含独立的、无状态的纯函数，用于执行特定的计算或任务。**

* **文件** **: **`<span class="selected">pathfinding.js</span>`, `<span class="selected">canvasUtils.js</span>`

#### 详细功能运行流程

* **应用启动与数据加载** **:**

1. **`<span class="selected">App.jsx</span>`** 作为入口，渲染主组件 `<span class="selected">FactoryCanvasApp</span>`。
2. `<span class="selected">FactoryCanvasApp</span>` 立即调用 **`<span class="selected">useMapData</span>`** Hook。
3. `<span class="selected">useMapData</span>` 负责通过 `<span class="selected">fetch</span>` API 异步加载 `<span class="selected">/map_data.json</span>`，并在此期间将 `<span class="selected">isLoading</span>` 状态设为 `<span class="selected">true</span>`，界面显示“加载中”。
4. **数据加载成功后，**`<span class="selected">useMapData</span>` 会处理数据（翻转Y轴、将节点数组转为Map对象以便快速访问），然后更新 `<span class="selected">mapData</span>` 状态并设置 `<span class="selected">isLoading</span>` 为 `<span class="selected">false</span>`。

* **地图渲染与视图交互 (CanvasRenderer.jsx)** **:**

1. `<span class="selected">CanvasRenderer</span>` 接收到 `<span class="selected">mapData</span>` 后，其核心 `<span class="selected">draw</span>` 函数被触发。
2. **`<span class="selected">draw</span>` 函数** **: 这是每一帧的渲染中心。它首先清空画布，然后根据 **`<span class="selected">scale</span>` (缩放) 和 `<span class="selected">offset</span>` (平移) 状态调整坐标系，最后**调用 `<span class="selected">canvasUtils.js</span>`** 中的各种绘图函数（`<span class="selected">drawNode</span>`, `<span class="selected">drawUnidirectionalEdge</span>` 等）来绘制所有静态地图元素。
3. **LOD (Level of Detail)** **: **`<span class="selected">draw</span>` 函数会检查当前的 `<span class="selected">scale</span>` 值，只有当用户放大到一定程度时（`<span class="selected">scale >= NODE_VISIBILITY_THRESHOLD</span>`），才会绘制节点和箭头等细节，极大地优化了缩小后的性能。
4. **缩放与平移** **: **`<span class="selected">CanvasRenderer</span>` 内部通过 `<span class="selected">wheel</span>` 事件监听器实现 **以鼠标为中心的缩放** **，并通过 **`<span class="selected">keydown</span>` (空格) 和 `<span class="selected">mousemove</span>` 事件实现画布的平移。所有复杂的坐标计算都封装在该组件内部。

* **路线规划与模拟启动** **:**

1. **用户在 ****`<span class="selected">Sidebar.jsx</span>`** 中选择起始和结束节点，然后点击“规划路线”按钮。
2. `<span class="selected">Sidebar.jsx</span>` 调用通过 props 传入的 `<span class="selected">onSetRoute</span>` 函数。
3. **这个 **`<span class="selected">onSetRoute</span>` 函数实际上是 **`<span class="selected">useSimulation</span>`** Hook 返回的 `<span class="selected">setRoute</span>` 函数。
4. **`<span class="selected">setRoute</span>` 函数** **:**
   * **它首先调用 ****`<span class="selected">findPath</span>`** 工具函数来获取一条包含时间和步骤的路径。
   * **然后，它更新自己的内部状态 **`<span class="selected">simulationData</span>`，创建一台新的小车，并将路径信息存入。
   * **最后，它将 **`<span class="selected">currentTime</span>` 重置为 `<span class="selected">0</span>` 并将 `<span class="selected">isPlaying</span>` 状态设为 `<span class="selected">true</span>`，从而 **启动模拟** **。**

* **小车运动与时间轴** **:**

1. **`<span class="selected">useSimulation</span>`** Hook 中的 `<span class="selected">useEffect</span>` 侦测到 `<span class="selected">isPlaying</span>` 变为 `<span class="selected">true</span>`，于是启动一个 `<span class="selected">requestAnimationFrame</span>` 动画循环。
2. **在每一帧，这个循环都会平滑地增加 **`<span class="selected">currentTime</span>` 状态。
3. `<span class="selected">currentTime</span>` 的改变会触发 **`<span class="selected">calculateCarPosition</span>`** 函数的重新计算。此函数根据当前时间在路径中的位置，通过**线性插值**计算出小车精确的 `<span class="selected">x, y</span>` 坐标和行驶 `<span class="selected">angle</span>` (角度)。
4. **计算出的新位置信息 (**`<span class="selected">dynamicElements</span>`) 被传递给 `<span class="selected">CanvasRenderer</span>`，`<span class="selected">draw</span>` 函数在下一帧调用 `<span class="selected">drawCar</span>` 工具函数，画出小车的新位置。
5. **同时，**`<span class="selected">currentTime</span>` 和控制函数 (`<span class="selected">play</span>`, `<span class="selected">pause</span>`, `<span class="selected">setTime</span>`) 也被传递给 **`<span class="selected">Timeline.jsx</span>`** 组件，用户可以通过播放/暂停按钮或拖动滑块来与模拟进行交互，进而改变 `<span class="selected">useSimulation</span>` 中的状态，形成一个完整的数据流闭环。
