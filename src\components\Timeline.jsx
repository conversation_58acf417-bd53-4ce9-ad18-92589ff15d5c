import React from 'react';

// Icon components
const PlayIcon = React.memo(() => (<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>));
const PauseIcon = React.memo(() => (<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"><rect x="6" y="4" width="4" height="16"></rect><rect x="14" y="4" width="4" height="16"></rect></svg>));

// ====================================================================================
// --- FILE: src/components/Timeline.js ---
// 用于控制模拟的新的时间轴组件。
// ====================================================================================
export const Timeline = ({ currentTime, maxTime, isPlaying, onPlay, onPause, onTimeChange }) => {
    if (maxTime <= 0) return null;

    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
    };

    return (
        <div className="absolute bottom-0 left-0 right-0 h-20 bg-gray-900 bg-opacity-90 backdrop-blur-sm z-20 flex items-center px-6 gap-4">
            <button onClick={isPlaying ? onPause : onPlay} className="text-white hover:text-indigo-400 transition-colors duration-200">
                {isPlaying ? <PauseIcon /> : <PlayIcon />}
            </button>
            <span className="text-white font-mono text-sm min-w-[3rem]">{formatTime(currentTime)}</span>
            <input
                type="range"
                min="0"
                max={maxTime}
                step="0.1"
                value={currentTime}
                onChange={(e) => onTimeChange(parseFloat(e.target.value))}
                className="flex-grow h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            />
            <span className="text-white font-mono text-sm min-w-[3rem]">{formatTime(maxTime)}</span>
        </div>
    );
};
