import React, { useState, useEffect, useMemo } from 'react';

// Icon Components
const ZoomInIcon = React.memo(() => (<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="11" y1="8" x2="11" y2="14"></line><line x1="8" y1="11" x2="14" y2="11"></line></svg>));
const ZoomOutIcon = React.memo(() => (<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="8" y1="11" x2="14" y2="11"></line></svg>));
const ResetIcon = React.memo(() => (<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 2v6h6"></path><path d="M21 12A9 9 0 0 0 6 5.3L3 8"></path><path d="M21 22v-6h-6"></path><path d="M3 12a9 9 0 0 0 15 6.7l3-2.7"></path></svg>));
const MapIcon = React.memo(() => (<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polygon points="3 6 9 3 15 6 21 3 21 18 15 21 9 18 3 21"></polygon><line x1="9" y1="3" x2="9" y2="18"></line><line x1="15" y1="6" x2="15" y2="21"></line></svg>));
const RouteIcon = React.memo(() => (<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 7v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2z"></path><polyline points="8,11 12,15 16,11"></polyline></svg>));
const PlayIcon = React.memo(() => (<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>));

// ====================================================================================
// --- FILE: src/components/Navigation.jsx ---
// 新的导航栏组件，替代原来的手风琴式侧边栏。
// ====================================================================================
export const Navigation = ({ mapData, selectedScheme, onZoomIn, onZoomOut, onResetView, onSetRoute, onBackToSchemes, selectedElement }) => {
    const [activeTab, setActiveTab] = useState('view');
    const [startNode, setStartNode] = useState('');
    const [endNode, setEndNode] = useState('');
    const [isSelectingStart, setIsSelectingStart] = useState(false);
    const [isSelectingEnd, setIsSelectingEnd] = useState(false);

    // 当选中元素是节点时，自动填充到路线规划
    useEffect(() => {
        if (selectedElement && selectedElement.type === 'node' && selectedElement.data?.id) {
            if (isSelectingStart) {
                setStartNode(selectedElement.data.id);
                setIsSelectingStart(false);
            } else if (isSelectingEnd) {
                setEndNode(selectedElement.data.id);
                setIsSelectingEnd(false);
            }
        }
    }, [selectedElement, isSelectingStart, isSelectingEnd]);

    const handleCalculateRoute = () => {
        if (startNode && endNode && startNode !== endNode) {
            onSetRoute(startNode, endNode);
        } else {
            alert("请为路线选择两个不同的节点。");
        }
    };

    const navItems = [
        { id: 'view', label: '视图控制', icon: <MapIcon /> },
        { id: 'route', label: '路线规划', icon: <RouteIcon /> },
        { id: 'simulation', label: '模拟控制', icon: <PlayIcon /> }
    ];

    return (
        <div className="h-full w-64 bg-gray-900 text-white shadow-lg flex flex-col">
            {/* Header */}
            <div className="p-4 border-b border-gray-700">
                <h1 className="text-xl font-bold">FactoryTwin</h1>
                <p className="text-sm text-gray-400">v2.0 模拟</p>
            </div>

            {/* 当前方案信息 */}
            {selectedScheme && (
                <div className="p-4 border-b border-gray-700 bg-gray-800">
                    <div className="flex items-center justify-between mb-2">
                        <h3 className="text-sm font-medium text-gray-300">当前方案</h3>
                        <button
                            onClick={onBackToSchemes}
                            className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
                        >
                            返回选择
                        </button>
                    </div>
                    <p className="text-white font-medium text-sm">{selectedScheme.name}</p>
                    <p className="text-xs text-gray-400 mt-1">{selectedScheme.description}</p>
                </div>
            )}

            {/* Navigation Tabs */}
            <div className="flex-shrink-0 border-b border-gray-700">
                {navItems.map(item => (
                    <button
                        key={item.id}
                        onClick={() => setActiveTab(item.id)}
                        className={`w-full flex items-center gap-3 px-4 py-3 text-left transition-colors duration-200 ${
                            activeTab === item.id 
                                ? 'bg-indigo-600 text-white' 
                                : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                        }`}
                    >
                        {item.icon}
                        <span className="text-sm font-medium">{item.label}</span>
                    </button>
                ))}
            </div>

            {/* Content Area */}
            <div className="flex-grow overflow-y-auto p-4">
                {activeTab === 'view' && (
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold text-white mb-3">视图控制</h3>
                        <div className="grid grid-cols-1 gap-2">
                            <button 
                                onClick={onZoomIn} 
                                className="flex items-center gap-2 bg-gray-700 p-3 rounded-lg hover:bg-gray-600 transition-colors duration-200"
                            >
                                <ZoomInIcon />
                                <span>放大</span>
                            </button>
                            <button 
                                onClick={onZoomOut} 
                                className="flex items-center gap-2 bg-gray-700 p-3 rounded-lg hover:bg-gray-600 transition-colors duration-200"
                            >
                                <ZoomOutIcon />
                                <span>缩小</span>
                            </button>
                            <button 
                                onClick={onResetView} 
                                className="flex items-center gap-2 bg-gray-700 p-3 rounded-lg hover:bg-gray-600 transition-colors duration-200"
                            >
                                <ResetIcon />
                                <span>重置视图</span>
                            </button>
                        </div>
                    </div>
                )}

                {activeTab === 'route' && (
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold text-white mb-3">路线规划</h3>
                        <div className="space-y-3">
                            <div>
                                <label htmlFor="start-node" className="block text-sm font-medium text-gray-300 mb-1">起始节点</label>
                                <div className="flex gap-2">
                                    <input
                                        id="start-node"
                                        type="text"
                                        value={startNode}
                                        onChange={e => setStartNode(e.target.value)}
                                        placeholder="输入节点ID或点击地图选择"
                                        className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                    />
                                    <button
                                        onClick={() => {
                                            setIsSelectingStart(true);
                                            setIsSelectingEnd(false);
                                        }}
                                        className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                                            isSelectingStart
                                                ? 'bg-indigo-600 text-white'
                                                : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                                        }`}
                                        title="点击地图选择起始节点"
                                    >
                                        选择
                                    </button>
                                </div>
                                {isSelectingStart && (
                                    <p className="text-xs text-indigo-400 mt-1">请在地图上点击一个节点作为起始点</p>
                                )}
                            </div>
                            <div>
                                <label htmlFor="end-node" className="block text-sm font-medium text-gray-300 mb-1">结束节点</label>
                                <div className="flex gap-2">
                                    <input
                                        id="end-node"
                                        type="text"
                                        value={endNode}
                                        onChange={e => setEndNode(e.target.value)}
                                        placeholder="输入节点ID或点击地图选择"
                                        className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                    />
                                    <button
                                        onClick={() => {
                                            setIsSelectingEnd(true);
                                            setIsSelectingStart(false);
                                        }}
                                        className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                                            isSelectingEnd
                                                ? 'bg-indigo-600 text-white'
                                                : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                                        }`}
                                        title="点击地图选择结束节点"
                                    >
                                        选择
                                    </button>
                                </div>
                                {isSelectingEnd && (
                                    <p className="text-xs text-indigo-400 mt-1">请在地图上点击一个节点作为终点</p>
                                )}
                            </div>
                            <button
                                onClick={handleCalculateRoute}
                                disabled={!startNode || !endNode}
                                className="w-full bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                            >
                                规划路线
                            </button>
                        </div>
                    </div>
                )}

                {activeTab === 'simulation' && (
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold text-white mb-3">模拟控制</h3>
                        <div className="text-sm text-gray-400 bg-gray-800 p-3 rounded-lg">
                            <p>规划路线后，使用底部的时间轴来控制模拟播放。</p>
                            <ul className="mt-2 space-y-1 text-xs">
                                <li>• 点击播放/暂停按钮控制模拟</li>
                                <li>• 拖动时间轴跳转到指定时间</li>
                                <li>• 观察AGV在地图上的移动轨迹</li>
                            </ul>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};
