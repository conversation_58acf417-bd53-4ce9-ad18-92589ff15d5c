import { useState } from 'react';
import { SchemeManager } from './SchemeManager';
import { Anomaly<PERSON>he<PERSON> } from './AnomalyChecker';

// 图标组件
const ChevronLeftIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
    </svg>
);

const ChevronRightIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
    </svg>
);

const ZoomInIcon = () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v6m3-3H7" />
    </svg>
);

const ZoomOutIcon = () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM7 10h6" />
    </svg>
);

const ResetIcon = () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
    </svg>
);

const PlusIcon = () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
    </svg>
);

const SettingsIcon = () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    </svg>
);

// 模拟方案数据
const DEMO_SCHEMES = [
    {
        id: 'scheme-1',
        name: '工厂布局方案 A',
        description: '标准生产线布局',
        mapDataUrl: '/map_data.json',
        roadDataUrl: '/road_data.json',
        status: 'active'
    },
    {
        id: 'scheme-2',
        name: '工厂布局方案 B',
        description: '高效流水线布局',
        mapDataUrl: '/map_data.json',
        roadDataUrl: '/road_data.json',
        status: 'draft'
    },
    {
        id: 'scheme-3',
        name: '工厂布局方案 C',
        description: '柔性制造布局',
        mapDataUrl: '/map_data.json',
        roadDataUrl: '/road_data.json',
        status: 'active'
    }
];

export const Sidebar = ({
    collapsed,
    onToggleCollapse,
    currentScheme,
    onSchemeChange,
    onZoomIn,
    onZoomOut,
    onResetView,
    isAddingMode,
    onToggleAddMode,
    layerSettings,
    onLayerSettingsChange,
    mapData,
    onHighlightElement
}) => {
    const [activeTab, setActiveTab] = useState(() => {
        // 从localStorage读取活动标签
        return localStorage.getItem('sidebar_activeTab') || 'view';
    });
    const [schemes] = useState(DEMO_SCHEMES);

    // 保存活动标签到localStorage
    const handleTabChange = (tabId) => {
        setActiveTab(tabId);
        localStorage.setItem('sidebar_activeTab', tabId);
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'draft': return 'bg-yellow-100 text-yellow-800';
            case 'archived': return 'bg-gray-100 text-gray-800';
            default: return 'bg-blue-100 text-blue-800';
        }
    };

    const getStatusText = (status) => {
        switch (status) {
            case 'active': return '已激活';
            case 'draft': return '草稿';
            case 'archived': return '已归档';
            default: return '未知';
        }
    };

    return (
        <div className={`bg-white border-r border-gray-200 transition-all duration-300 flex flex-col ${
            collapsed ? 'w-16' : 'w-80'
        }`}>
            {/* 头部 */}
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                {!collapsed && (
                    <div>
                        <h1 className="text-lg font-bold text-gray-900">工厂数字孪生</h1>
                        <p className="text-sm text-gray-500">Factory Digital Twin</p>
                    </div>
                )}
                <button
                    onClick={onToggleCollapse}
                    className="p-2 rounded-lg hover:bg-gray-100 text-gray-600 transition-colors"
                >
                    {collapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
                </button>
            </div>

            {!collapsed && (
                <>
                    {/* 当前方案信息 */}
                    <div className="p-4 border-b border-gray-200 bg-gray-50">
                        <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-700">当前方案</span>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(currentScheme.status)}`}>
                                {getStatusText(currentScheme.status)}
                            </span>
                        </div>
                        <h3 className="font-medium text-gray-900">{currentScheme.name}</h3>
                        <p className="text-sm text-gray-600 mt-1">{currentScheme.description}</p>
                    </div>

                    {/* 导航标签 */}
                    <div className="flex border-b border-gray-200">
                        {[
                            { id: 'view', label: '视图控制' },
                            { id: 'schemes', label: '方案管理' },
                            { id: 'anomaly', label: '异常检查' }
                        ].map(tab => (
                            <button
                                key={tab.id}
                                onClick={() => handleTabChange(tab.id)}
                                className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                                    activeTab === tab.id
                                        ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                                }`}
                            >
                                {tab.label}
                            </button>
                        ))}
                    </div>

                    {/* 内容区域 */}
                    <div className="flex-1 overflow-y-auto p-4">
                        {activeTab === 'view' && (
                            <div className="space-y-6">
                                {/* 视图控制 */}
                                <div>
                                    <h3 className="text-sm font-medium text-gray-900 mb-3">视图控制</h3>
                                    <div className="grid grid-cols-3 gap-2">
                                        <button
                                            onClick={onZoomIn}
                                            className="flex items-center justify-center p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                                            title="放大"
                                        >
                                            <ZoomInIcon />
                                        </button>
                                        <button
                                            onClick={onZoomOut}
                                            className="flex items-center justify-center p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                                            title="缩小"
                                        >
                                            <ZoomOutIcon />
                                        </button>
                                        <button
                                            onClick={onResetView}
                                            className="flex items-center justify-center p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                                            title="重置视图"
                                        >
                                            <ResetIcon />
                                        </button>
                                    </div>
                                </div>

                                {/* 节点操作 */}
                                <div>
                                    <h3 className="text-sm font-medium text-gray-900 mb-3">节点操作</h3>
                                    <button
                                        onClick={onToggleAddMode}
                                        className={`w-full flex items-center justify-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                                            isAddingMode
                                                ? 'bg-red-100 text-red-700 border border-red-300 hover:bg-red-200'
                                                : 'bg-blue-100 text-blue-700 border border-blue-300 hover:bg-blue-200'
                                        }`}
                                    >
                                        <PlusIcon />
                                        {isAddingMode ? '取消新增' : '新增节点'}
                                    </button>
                                    {isAddingMode && (
                                        <p className="text-xs text-gray-600 mt-2">
                                            点击地图上的路线来添加新节点
                                        </p>
                                    )}
                                </div>

                                {/* 图层控制 */}
                                <div>
                                    <h3 className="text-sm font-medium text-gray-900 mb-3">图层显示</h3>
                                    <div className="space-y-3">
                                        <div>
                                            <label className="flex items-center justify-between">
                                                <span className="text-sm text-gray-700">道路系统</span>
                                                <input
                                                    type="checkbox"
                                                    checked={layerSettings.showRoads}
                                                    onChange={(e) => onLayerSettingsChange({ showRoads: e.target.checked })}
                                                    className="rounded border-gray-300"
                                                />
                                            </label>
                                            {layerSettings.showRoads && (
                                                <div className="mt-2 ml-4">
                                                    <label className="text-xs text-gray-600">透明度</label>
                                                    <input
                                                        type="range"
                                                        min="0.1"
                                                        max="1"
                                                        step="0.1"
                                                        value={layerSettings.roadOpacity}
                                                        onChange={(e) => onLayerSettingsChange({ roadOpacity: parseFloat(e.target.value) })}
                                                        className="w-full h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                                                    />
                                                    <span className="text-xs text-gray-500">{Math.round(layerSettings.roadOpacity * 100)}%</span>
                                                </div>
                                            )}
                                        </div>

                                        <div>
                                            <label className="flex items-center justify-between">
                                                <span className="text-sm text-gray-700">节点标记</span>
                                                <input
                                                    type="checkbox"
                                                    checked={layerSettings.showNodes}
                                                    onChange={(e) => onLayerSettingsChange({ showNodes: e.target.checked })}
                                                    className="rounded border-gray-300"
                                                />
                                            </label>
                                            {layerSettings.showNodes && (
                                                <div className="mt-2 ml-4">
                                                    <label className="text-xs text-gray-600">透明度</label>
                                                    <input
                                                        type="range"
                                                        min="0.1"
                                                        max="1"
                                                        step="0.1"
                                                        value={layerSettings.nodeOpacity}
                                                        onChange={(e) => onLayerSettingsChange({ nodeOpacity: parseFloat(e.target.value) })}
                                                        className="w-full h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                                                    />
                                                    <span className="text-xs text-gray-500">{Math.round(layerSettings.nodeOpacity * 100)}%</span>
                                                </div>
                                            )}
                                        </div>

                                        <div>
                                            <label className="flex items-center justify-between">
                                                <span className="text-sm text-gray-700">连接线</span>
                                                <input
                                                    type="checkbox"
                                                    checked={layerSettings.showEdges}
                                                    onChange={(e) => onLayerSettingsChange({ showEdges: e.target.checked })}
                                                    className="rounded border-gray-300"
                                                />
                                            </label>
                                            {layerSettings.showEdges && (
                                                <div className="mt-2 ml-4">
                                                    <label className="text-xs text-gray-600">透明度</label>
                                                    <input
                                                        type="range"
                                                        min="0.1"
                                                        max="1"
                                                        step="0.1"
                                                        value={layerSettings.edgeOpacity}
                                                        onChange={(e) => onLayerSettingsChange({ edgeOpacity: parseFloat(e.target.value) })}
                                                        className="w-full h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                                                    />
                                                    <span className="text-xs text-gray-500">{Math.round(layerSettings.edgeOpacity * 100)}%</span>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {activeTab === 'schemes' && (
                            <div className="space-y-4">
                                <h3 className="text-sm font-medium text-gray-900">方案列表</h3>
                                <div className="space-y-2">
                                    {schemes.map(scheme => (
                                        <div
                                            key={scheme.id}
                                            onClick={() => onSchemeChange(scheme)}
                                            className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                                                currentScheme.id === scheme.id
                                                    ? 'border-blue-300 bg-blue-50'
                                                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                                            }`}
                                        >
                                            <div className="flex items-center justify-between mb-1">
                                                <h4 className="text-sm font-medium text-gray-900">{scheme.name}</h4>
                                                <span className={`px-2 py-0.5 text-xs font-medium rounded ${getStatusColor(scheme.status)}`}>
                                                    {getStatusText(scheme.status)}
                                                </span>
                                            </div>
                                            <p className="text-xs text-gray-600">{scheme.description}</p>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {activeTab === 'anomaly' && (
                            <AnomalyChecker
                                mapData={mapData}
                                onHighlightElement={onHighlightElement}
                            />
                        )}
                    </div>
                </>
            )}

            {/* 折叠状态下的快捷按钮 */}
            {collapsed && (
                <div className="p-2 space-y-2">
                    <button
                        onClick={onZoomIn}
                        className="w-full p-2 rounded-lg hover:bg-gray-100 text-gray-600 transition-colors"
                        title="放大"
                    >
                        <ZoomInIcon />
                    </button>
                    <button
                        onClick={onZoomOut}
                        className="w-full p-2 rounded-lg hover:bg-gray-100 text-gray-600 transition-colors"
                        title="缩小"
                    >
                        <ZoomOutIcon />
                    </button>
                    <button
                        onClick={onToggleAddMode}
                        className={`w-full p-2 rounded-lg transition-colors ${
                            isAddingMode
                                ? 'bg-red-100 text-red-600'
                                : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                        }`}
                        title={isAddingMode ? '取消新增' : '新增节点'}
                    >
                        <PlusIcon />
                    </button>
                </div>
            )}
        </div>
    );
};
