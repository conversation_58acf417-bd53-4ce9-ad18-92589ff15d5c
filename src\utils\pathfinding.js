// ====================================================================================
// --- FILE: src/utils/pathfinding.js ---
// 改进的路径查找算法，支持使用实际的边缘数据进行路径规划
// ====================================================================================

// 构建图的邻接表
const buildGraph = (nodes, unidirectionalEdges, bidirectionalEdges) => {
    const graph = new Map();

    // 初始化所有节点
    nodes.forEach((_, nodeId) => {
        graph.set(nodeId, []);
    });

    // 添加单向边
    unidirectionalEdges.forEach(edge => {
        if (graph.has(edge.start_node_id)) {
            graph.get(edge.start_node_id).push({
                to: edge.end_node_id,
                weight: edge.travel_time || 5,
                distance: edge.distance || 100,
                edgeId: edge.id
            });
        }
    });

    // 添加双向边
    bidirectionalEdges.forEach(edge => {
        const [node1, node2] = edge.nodes;
        const weight = edge.travel_time || 5;
        const distance = edge.distance || 100;

        if (graph.has(node1)) {
            graph.get(node1).push({
                to: node2,
                weight,
                distance,
                edgeId: edge.id
            });
        }

        if (graph.has(node2)) {
            graph.get(node2).push({
                to: node1,
                weight,
                distance,
                edgeId: edge.id
            });
        }
    });

    return graph;
};

// 简化的Dijkstra算法实现
const dijkstra = (graph, startNode, endNode) => {
    const distances = new Map();
    const previous = new Map();
    const unvisited = new Set();

    // 初始化距离
    graph.forEach((_, nodeId) => {
        distances.set(nodeId, nodeId === startNode ? 0 : Infinity);
        unvisited.add(nodeId);
    });

    while (unvisited.size > 0) {
        // 找到未访问节点中距离最小的
        let currentNode = null;
        let minDistance = Infinity;

        unvisited.forEach(nodeId => {
            if (distances.get(nodeId) < minDistance) {
                minDistance = distances.get(nodeId);
                currentNode = nodeId;
            }
        });

        if (currentNode === null || minDistance === Infinity) break;

        unvisited.delete(currentNode);

        // 如果到达目标节点，停止搜索
        if (currentNode === endNode) break;

        // 更新邻居节点的距离
        const neighbors = graph.get(currentNode) || [];
        neighbors.forEach(neighbor => {
            if (unvisited.has(neighbor.to)) {
                const newDistance = distances.get(currentNode) + neighbor.weight;
                if (newDistance < distances.get(neighbor.to)) {
                    distances.set(neighbor.to, newDistance);
                    previous.set(neighbor.to, currentNode);
                }
            }
        });
    }

    // 重建路径
    const path = [];
    let currentNode = endNode;

    while (currentNode !== undefined) {
        path.unshift(currentNode);
        currentNode = previous.get(currentNode);
    }

    return path.length > 1 && path[0] === startNode ? path : null;
};

export const findPath = (mapData, startNodeId, endNodeId) => {
    if (!mapData || !mapData.nodes || mapData.nodes.size === 0 || !startNodeId || !endNodeId) {
        return null;
    }

    const { nodes, unidirectional_edges = [], bidirectional_edges = [] } = mapData;

    // 如果没有边缘数据，使用简单的直线路径
    if (unidirectional_edges.length === 0 && bidirectional_edges.length === 0) {
        return findSimplePath(nodes, startNodeId, endNodeId);
    }

    // 构建图并查找路径
    const graph = buildGraph(nodes, unidirectional_edges, bidirectional_edges);
    const path = dijkstra(graph, startNodeId, endNodeId);

    if (!path || path.length < 2) {
        console.warn(`无法找到从 ${startNodeId} 到 ${endNodeId} 的路径`);
        return null;
    }

    // 计算路径的时间信息
    let cumulativeTime = 0;
    const pathWithTime = path.map((nodeId, index) => {
        const nodeData = {
            nodeId,
            time: cumulativeTime,
        };

        // 如果不是最后一个节点，计算到下一个节点的时间
        if (index < path.length - 1) {
            const nextNodeId = path[index + 1];
            const neighbors = graph.get(nodeId) || [];
            const edge = neighbors.find(n => n.to === nextNodeId);
            cumulativeTime += edge ? edge.weight : 5; // 默认5秒
        }

        return nodeData;
    });

    return {
        totalTime: cumulativeTime,
        steps: pathWithTime,
    };
};

// 简单路径查找（当没有边缘数据时使用）
const findSimplePath = (nodes, startNodeId, endNodeId) => {
    const allNodeIds = Array.from(nodes.keys()).sort();
    const startIndex = allNodeIds.indexOf(startNodeId);
    const endIndex = allNodeIds.indexOf(endNodeId);

    if (startIndex === -1 || endIndex === -1) {
        console.error("未找到起始或结束节点");
        return null;
    }

    let path = [];
    if (startIndex <= endIndex) {
        for(let i = startIndex; i <= endIndex; i++) {
            path.push(allNodeIds[i]);
        }
    } else {
         for(let i = startIndex; i >= endIndex; i--) {
            path.push(allNodeIds[i]);
        }
    }

    if (path.length < 2) return null;

    const travelTimePerSegment = 5;
    let cumulativeTime = 0;
    const pathWithTime = path.map(nodeId => {
        const nodeData = {
            nodeId,
            time: cumulativeTime,
        };
        cumulativeTime += travelTimePerSegment;
        return nodeData;
    });

    return {
        totalTime: (path.length - 1) * travelTimePerSegment,
        steps: pathWithTime,
    };
};