import { useState } from 'react';

export const SchemeManager = ({ schemes, onSchemeUpdate, onSchemeDelete, onSchemeCreate }) => {
    const [isCreating, setIsCreating] = useState(false);
    const [editingScheme, setEditingScheme] = useState(null);
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        status: 'draft'
    });

    const handleCreateNew = () => {
        setFormData({
            name: '',
            description: '',
            status: 'draft'
        });
        setIsCreating(true);
    };

    const handleEdit = (scheme) => {
        setFormData({
            name: scheme.name,
            description: scheme.description,
            status: scheme.status
        });
        setEditingScheme(scheme);
    };

    const handleSave = () => {
        if (isCreating) {
            const newScheme = {
                id: `scheme-${Date.now()}`,
                ...formData,
                mapDataUrl: '/map_data.json', // 默认使用现有的数据文件
                roadDataUrl: '/road_data.json',
                createdAt: new Date().toISOString().split('T')[0],
                thumbnail: '/thumbnails/default.png'
            };
            onSchemeCreate?.(newScheme);
            setIsCreating(false);
        } else if (editingScheme) {
            onSchemeUpdate?.(editingScheme.id, formData);
            setEditingScheme(null);
        }
        setFormData({ name: '', description: '', status: 'draft' });
    };

    const handleCancel = () => {
        setIsCreating(false);
        setEditingScheme(null);
        setFormData({ name: '', description: '', status: 'draft' });
    };

    const handleDelete = (schemeId) => {
        if (window.confirm('确定要删除这个方案吗？此操作不可撤销。')) {
            onSchemeDelete?.(schemeId);
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'draft': return 'bg-yellow-100 text-yellow-800';
            case 'archived': return 'bg-gray-100 text-gray-800';
            default: return 'bg-blue-100 text-blue-800';
        }
    };

    const getStatusText = (status) => {
        switch (status) {
            case 'active': return '已激活';
            case 'draft': return '草稿';
            case 'archived': return '已归档';
            default: return '未知';
        }
    };

    return (
        <div className="space-y-4">
            {/* 头部操作 */}
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-white">方案管理</h3>
                <button
                    onClick={handleCreateNew}
                    className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                >
                    新建方案
                </button>
            </div>

            {/* 创建/编辑表单 */}
            {(isCreating || editingScheme) && (
                <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
                    <h4 className="text-white font-medium mb-3">
                        {isCreating ? '创建新方案' : '编辑方案'}
                    </h4>
                    
                    <div className="space-y-3">
                        <div>
                            <label className="block text-sm text-gray-300 mb-1">方案名称</label>
                            <input
                                type="text"
                                value={formData.name}
                                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                                className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
                                placeholder="输入方案名称"
                            />
                        </div>
                        
                        <div>
                            <label className="block text-sm text-gray-300 mb-1">描述</label>
                            <textarea
                                value={formData.description}
                                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                                className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
                                rows="2"
                                placeholder="输入方案描述"
                            />
                        </div>
                        
                        <div>
                            <label className="block text-sm text-gray-300 mb-1">状态</label>
                            <select
                                value={formData.status}
                                onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                                className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
                            >
                                <option value="draft">草稿</option>
                                <option value="active">已激活</option>
                                <option value="archived">已归档</option>
                            </select>
                        </div>
                    </div>
                    
                    <div className="flex space-x-2 mt-4">
                        <button
                            onClick={handleSave}
                            disabled={!formData.name.trim()}
                            className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors"
                        >
                            保存
                        </button>
                        <button
                            onClick={handleCancel}
                            className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 transition-colors"
                        >
                            取消
                        </button>
                    </div>
                </div>
            )}

            {/* 方案列表 */}
            <div className="space-y-2">
                {schemes.map((scheme) => (
                    <div
                        key={scheme.id}
                        className="bg-gray-800 p-3 rounded border border-gray-700 hover:border-gray-600 transition-colors"
                    >
                        <div className="flex items-center justify-between">
                            <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                    <h4 className="text-white font-medium text-sm">{scheme.name}</h4>
                                    <span className={`px-2 py-0.5 text-xs font-medium rounded ${getStatusColor(scheme.status)}`}>
                                        {getStatusText(scheme.status)}
                                    </span>
                                </div>
                                <p className="text-gray-400 text-xs">{scheme.description}</p>
                                <p className="text-gray-500 text-xs mt-1">创建: {scheme.createdAt}</p>
                            </div>
                            
                            <div className="flex space-x-1">
                                <button
                                    onClick={() => handleEdit(scheme)}
                                    className="p-1 text-blue-400 hover:text-blue-300 transition-colors"
                                    title="编辑"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                </button>
                                <button
                                    onClick={() => handleDelete(scheme.id)}
                                    className="p-1 text-red-400 hover:text-red-300 transition-colors"
                                    title="删除"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {schemes.length === 0 && (
                <div className="text-center py-8 text-gray-400">
                    <p className="text-sm">暂无方案</p>
                    <p className="text-xs mt-1">点击"新建方案"开始创建</p>
                </div>
            )}
        </div>
    );
};
