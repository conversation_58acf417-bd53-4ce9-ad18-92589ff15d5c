import { useState, useEffect, useMemo } from 'react';

// ====================================================================================
// --- FILE: src/hooks/useDynamicState.js ---
// 管理动态元素状态的Hook，包括小车和托盘的实时状态
// ====================================================================================

/**
 * 动态状态管理Hook
 * @param {Object} mapData - 静态地图数据
 * @param {Object} simulationData - 仿真数据
 * @param {number} currentTime - 当前仿真时间
 * @returns {Object} 动态状态数据
 */
export const useDynamicState = (mapData, simulationData, currentTime) => {
    // 动态状态存储
    const [dynamicState, setDynamicState] = useState({
        cars: [],
        pallets: [],
        lastUpdateTime: 0
    });

    // 预留接口：从外部JSON更新动态状态
    const updateDynamicStateFromJson = (jsonData) => {
        try {
            const parsedData = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;
            
            setDynamicState(prevState => ({
                ...prevState,
                cars: parsedData.cars || prevState.cars,
                pallets: parsedData.pallets || prevState.pallets,
                lastUpdateTime: Date.now()
            }));
        } catch (error) {
            console.error('更新动态状态失败:', error);
        }
    };

    // 预留接口：更新单个小车状态
    const updateCarState = (carId, newState) => {
        setDynamicState(prevState => ({
            ...prevState,
            cars: prevState.cars.map(car => 
                car.id === carId ? { ...car, ...newState } : car
            ),
            lastUpdateTime: Date.now()
        }));
    };

    // 预留接口：更新单个托盘状态
    const updatePalletState = (palletId, newState) => {
        setDynamicState(prevState => ({
            ...prevState,
            pallets: prevState.pallets.map(pallet => 
                pallet.id === palletId ? { ...pallet, ...newState } : pallet
            ),
            lastUpdateTime: Date.now()
        }));
    };

    // 初始化动态状态（创建示例数据，因为静态地图数据中没有vehicles和pallets）
    useEffect(() => {
        if (!mapData || mapData.nodes.size === 0) return;

        // 获取前几个节点作为初始位置
        const nodeArray = Array.from(mapData.nodes.keys());

        // 创建示例小车数据
        const initialCars = [
            {
                id: 'agv_001',
                name: 'AGV-001',
                status: 'idle',
                battery: 85,
                location: nodeArray[0] || null,
                // 动态状态字段
                isRunning: false,
                isRotating: false,
                rotationAngle: 0,
                speed: 0,
                targetPosition: null,
                currentTask: null
            },
            {
                id: 'agv_002',
                name: 'AGV-002',
                status: 'active',
                battery: 92,
                location: nodeArray[Math.min(1, nodeArray.length - 1)] || null,
                // 动态状态字段
                isRunning: true,
                isRotating: false,
                rotationAngle: 45,
                speed: 1.5,
                targetPosition: null,
                currentTask: 'transport'
            },
            {
                id: 'agv_003',
                name: 'AGV-003',
                status: 'active',
                battery: 92,
                location: nodeArray[Math.min(3, nodeArray.length - 1)] || null,
                // 动态状态字段
                isRunning: true,
                isRotating: false,
                rotationAngle: 45,
                speed: 1.5,
                targetPosition: null,
                currentTask: 'transport'
            }
        ];

        // 创建示例托盘数据
        const initialPallets = [
            {
                id: 'pallet_001',
                shape: 'standard',
                status: 'loaded',
                contents: 'Parts A-Z',
                weight: 150,
                location: nodeArray[0] || null,
                // 动态状态字段
                isMoving: false,
                isLoaded: true,
                currentCarrier: 'agv_001', // 被AGV-001载着
                targetLocation: null
            },
            {
                id: 'pallet_002',
                shape: 'large',
                status: 'loaded',
                contents: 'Equipment Parts',
                weight: 200,
                location: nodeArray[Math.min(1, nodeArray.length - 1)] || null,
                // 动态状态字段
                isMoving: true,
                isLoaded: true,
                currentCarrier: 'agv_002', // 被AGV-002载着
                targetLocation: null
            }
        ];

        setDynamicState({
            cars: initialCars,
            pallets: initialPallets,
            lastUpdateTime: Date.now()
        });
    }, [mapData]);

    // 计算带位置信息的动态元素和关联关系
    const dynamicElementsWithPositions = useMemo(() => {
        const carsWithPositions = dynamicState.cars.map((car, index) => {
            let position = null;

            // 如果小车有位置信息，使用节点的坐标
            if (car.location && mapData.nodes.has(car.location)) {
                const locationNode = mapData.nodes.get(car.location);
                position = {
                    x: locationNode.x,
                    y: locationNode.y,
                    angle: car.rotationAngle || 0
                };
            }

            // 如果仍然没有位置，使用默认位置
            if (!position) {
                const nodeArray = Array.from(mapData.nodes.values());
                if (nodeArray.length > 0) {
                    const defaultNode = nodeArray[index % nodeArray.length];
                    position = {
                        x: defaultNode.x + 30,
                        y: defaultNode.y + 30,
                        angle: 0
                    };
                }
            }

            return position ? { ...car, position } : null;
        }).filter(car => car !== null);

        const palletsWithPositions = dynamicState.pallets.map((pallet, index) => {
            // 如果托盘被小车载着，使用小车的位置
            if (pallet.currentCarrier) {
                const carrierCar = carsWithPositions.find(car => car.id === pallet.currentCarrier);
                if (carrierCar) {
                    return {
                        ...pallet,
                        position: {
                            x: carrierCar.position.x,
                            y: carrierCar.position.y,
                            angle: carrierCar.position.angle
                        },
                        isCarried: true
                    };
                }
            }

            // 如果托盘有独立位置信息，使用节点的坐标
            if (pallet.location && mapData.nodes.has(pallet.location)) {
                const locationNode = mapData.nodes.get(pallet.location);
                // 为了避免重叠，给托盘添加小的偏移
                const offsetX = (index % 3 - 1) * 8;
                const offsetY = (Math.floor(index / 3) % 3 - 1) * 8;
                return {
                    ...pallet,
                    position: {
                        x: locationNode.x + offsetX,
                        y: locationNode.y + offsetY
                    },
                    isCarried: false
                };
            }

            // 使用地图中心附近的位置
            const nodeArray = Array.from(mapData.nodes.values());
            if (nodeArray.length > 0) {
                const centerX = nodeArray.reduce((sum, node) => sum + node.x, 0) / nodeArray.length;
                const centerY = nodeArray.reduce((sum, node) => sum + node.y, 0) / nodeArray.length;
                return {
                    ...pallet,
                    position: {
                        x: centerX + (index % 5 - 2) * 25,
                        y: centerY + (Math.floor(index / 5) % 5 - 2) * 25
                    },
                    isCarried: false
                };
            }

            return {
                ...pallet,
                position: {
                    x: 100 + (index % 3) * 50,
                    y: 100 + Math.floor(index / 3) * 50
                },
                isCarried: false
            };
        });

        // 创建小车-托盘配对关系，用于优化渲染
        const carPalletPairs = [];
        carsWithPositions.forEach(car => {
            const carriedPallet = palletsWithPositions.find(pallet =>
                pallet.currentCarrier === car.id && pallet.isCarried
            );
            if (carriedPallet) {
                carPalletPairs.push({
                    car,
                    pallet: carriedPallet,
                    id: `${car.id}_${carriedPallet.id}`
                });
            }
        });

        // 过滤掉被载着的托盘，避免重复渲染
        const independentPallets = palletsWithPositions.filter(pallet => !pallet.isCarried);
        const independentCars = carsWithPositions.filter(car =>
            !carPalletPairs.some(pair => pair.car.id === car.id)
        );

        return {
            cars: independentCars,
            pallets: independentPallets,
            carPalletPairs
        };
    }, [dynamicState, mapData.nodes]);

    return {
        dynamicState,
        dynamicElements: dynamicElementsWithPositions,
        updateDynamicStateFromJson,
        updateCarState,
        updatePalletState
    };
};
