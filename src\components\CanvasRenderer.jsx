import React, { useState, useRef, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';
import {
    drawNode,
    drawUnidirectionalEdge,
    drawBidirectionalEdge,
    drawCar,
    drawPallet,
    drawCarWithPallet
} from '../utils/canvasUtils';
import { drawRoad, detectRoadClick } from '../utils/roadDrawing';

// ====================================================================================
// --- FILE: src/components/CanvasRenderer.js ---
// 处理所有 Canvas 渲染和交互的专用组件。
// ====================================================================================
export const CanvasRenderer = forwardRef(({ 
    mapData, 
    roadData, 
    dynamicElements, 
    selectedElement, 
    onElementClick, 
    onRoadPointAdd,
    onRoadPointRemove,
    showDynamicElements = true 
}, ref) => {
    const canvasRef = useRef(null);
    const containerRef = useRef(null);

    const [scale, setScale] = useState(1);
    const [offset, setOffset] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [isSpacePressed, setIsSpacePressed] = useState(false);
    const lastMousePosition = useRef({ x: 0, y: 0 });

    const MIN_SCALE = 0.000001;
    const MAX_SCALE = 5;
    const ZOOM_SENSITIVITY = 0.001;

    // --- 绘图逻辑 ---
    const draw = useCallback(() => {
        const canvas = canvasRef.current;
        if (!canvas || !mapData.nodes) return;

        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        ctx.save();
        ctx.translate(offset.x, offset.y);
        ctx.scale(scale, scale);

        // 1. 绘制双向边
        mapData.bidirectional_edges?.forEach(edge => {
            const isSelected = selectedElement?.type === 'edge' && selectedElement?.data?.id === edge.id;
            drawBidirectionalEdge(ctx, edge, mapData.nodes, scale, isSelected);
        });

        // 2. 绘制单向边
        mapData.unidirectional_edges?.forEach(edge => {
            const isSelected = selectedElement?.type === 'edge' && selectedElement?.data?.id === edge.id;
            drawUnidirectionalEdge(ctx, edge, mapData.nodes, scale, isSelected);
        });

        // 3. 绘制道路（倒数第二层）
        if (roadData.currentScheme?.roads) {
            roadData.currentScheme.roads.forEach(road => {
                const isSelected = selectedElement?.type === 'road_point' && selectedElement?.data?.roadId === road.id;
                drawRoad(ctx, road, mapData.nodes, scale, isSelected);
            });
        }

        // 4. 绘制节点
        if (scale >= NODE_VISIBILITY_THRESHOLD) {
            mapData.nodes.forEach(node => {
                const isSelected = selectedElement?.type === 'node' && selectedElement?.data?.id === node.id;
                drawNode(ctx, node, scale, isSelected);
            });
        }

        // 5. 绘制动态元素
        if (showDynamicElements && dynamicElements) {
            dynamicElements.pallets?.forEach(pallet => {
                const isSelected = selectedElement?.type === 'pallet' && selectedElement?.data?.id === pallet.id;
                drawPallet(ctx, pallet, scale, isSelected);
            });

            dynamicElements.cars?.forEach(car => {
                const isSelected = selectedElement?.type === 'car' && selectedElement?.data?.id === car.id;
                drawCar(ctx, car, scale, isSelected);
            });
        }

        ctx.restore();
    }, [scale, offset, mapData, roadData, dynamicElements, selectedElement, showDynamicElements]);

    useEffect(() => {
        draw();
    }, [draw]);

    // --- 视图控制 (缩放, 平移, 重置) ---
    const resetView = useCallback(() => {
        const { nodes } = mapData;
        const container = containerRef.current;
        if (!container || nodes.size === 0) return;

        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
        nodes.forEach(node => {
            if (node.x < minX) minX = node.x;
            if (node.y < minY) minY = node.y;
            if (node.x > maxX) maxX = node.x;
            if (node.y > maxY) maxY = node.y;
        });

        const { width: viewWidth, height: viewHeight } = container.getBoundingClientRect();
        const mapWidth = maxX - minX;
        const mapHeight = maxY - minY;
        const mapCenterX = minX + mapWidth / 2;
        const mapCenterY = minY + mapHeight / 2;

        const scaleX = mapWidth > 0 ? (viewWidth / mapWidth) * 0.9 : 1;
        const scaleY = mapHeight > 0 ? (viewHeight / mapHeight) * 0.9 : 1;
        const initialScale = Math.min(scaleX, scaleY, MAX_SCALE);

        setOffset({
            x: viewWidth / 2 - mapCenterX * initialScale,
            y: viewHeight / 2 - mapCenterY * initialScale,
        });
        setScale(initialScale);
    }, [mapData]);

    useEffect(() => {
        // 数据加载后自动重置视图
        if (mapData.nodes.size > 0) {
            resetView();
        }
    }, [mapData.nodes, resetView]);

    // 缩放控制函数
    const zoomIn = useCallback(() => {
        const newScale = Math.min(MAX_SCALE, scale * 1.05);
        setScale(newScale);
    }, [scale]);

    const zoomOut = useCallback(() => {
        const newScale = Math.max(MIN_SCALE, scale / 1.05);
        setScale(newScale);
    }, [scale]);

    // 通过ref暴露控制方法
    useImperativeHandle(ref, () => ({
        zoomIn,
        zoomOut,
        resetView
    }), [zoomIn, zoomOut, resetView]);

    // --- 事件处理器 ---
    const handleWheel = useCallback((e) => {
        e.preventDefault();
        if (!canvasRef.current) return;
        const rect = canvasRef.current.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        const zoomFactor = 1 - e.deltaY * ZOOM_SENSITIVITY;
        const newScale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale * zoomFactor));
        const worldX = (mouseX - offset.x) / scale;
        const worldY = (mouseY - offset.y) / scale;
        setOffset({ x: mouseX - worldX * newScale, y: mouseY - worldY * newScale });
        setScale(newScale);
    }, [scale, offset]);

    // 处理元素点击选择
    const handleElementClick = useCallback((event) => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const rect = canvas.getBoundingClientRect();
        const x = (event.clientX - rect.left - offset.x) / scale;
        const y = (event.clientY - rect.top - offset.y) / scale;

        let clickedElement = null;

        // 检测道路点击
        if (roadData.currentScheme?.roads) {
            clickedElement = detectRoadClick(roadData.currentScheme.roads, mapData.nodes, x, y, scale);
        }

        // 如果没有点击道路，检测其他元素
        if (!clickedElement) {
            // ... 现有的节点、边、动态元素检测代码 ...
        }

        // 处理道路相关的点击事件
        if (clickedElement?.type === 'road_point') {
            // 右键删除点
            if (event.button === 2) {
                event.preventDefault();
                onRoadPointRemove?.(clickedElement.data.roadId, clickedElement.data.pointIndex);
                return;
            }
        } else if (clickedElement?.type === 'road_edge') {
            // 在道路边缘添加新点
            const nearestNodeId = findNearestNode(mapData.nodes, x, y);
            if (nearestNodeId) {
                onRoadPointAdd?.(clickedElement.data.roadId, nearestNodeId, clickedElement.data.insertIndex);
            }
            return;
        }

        onElementClick?.(clickedElement);
    }, [mapData, roadData, scale, offset, onElementClick, onRoadPointAdd, onRoadPointRemove]);

    const handleMouseDown = useCallback((e) => {
        if (isSpacePressed) {
            setIsDragging(true);
            lastMousePosition.current = { x: e.clientX, y: e.clientY };
            if (canvasRef.current) canvasRef.current.style.cursor = 'grabbing';
        } else {
            // 处理元素选择
            handleElementClick(e);
        }
    }, [isSpacePressed, handleElementClick]);

    // 计算点到线段的距离
    const distancePointToLine = (px, py, x1, y1, x2, y2) => {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;

        if (lenSq === 0) return Math.sqrt(A * A + B * B);

        let param = dot / lenSq;

        if (param < 0) {
            return Math.sqrt(A * A + B * B);
        } else if (param > 1) {
            const E = px - x2;
            const F = py - y2;
            return Math.sqrt(E * E + F * F);
        } else {
            const projX = x1 + param * C;
            const projY = y1 + param * D;
            const G = px - projX;
            const H = py - projY;
            return Math.sqrt(G * G + H * H);
        }
    };

    const handleMouseMove = useCallback((e) => {
        if (isDragging) {
            const dx = e.clientX - lastMousePosition.current.x;
            const dy = e.clientY - lastMousePosition.current.y;
            setOffset(prev => ({ x: prev.x + dx, y: prev.y + dy }));
            lastMousePosition.current = { x: e.clientX, y: e.clientY };
        }
    }, [isDragging]);

    const handleMouseUp = useCallback(() => {
        setIsDragging(false);
        if (canvasRef.current) canvasRef.current.style.cursor = isSpacePressed ? 'grab' : 'default';
    }, [isSpacePressed]);

    const handleKeyDown = useCallback((e) => {
        if (e.code === 'Space') {
            e.preventDefault();
            setIsSpacePressed(true);
            if (canvasRef.current && !isDragging) canvasRef.current.style.cursor = 'grab';
        }
    }, [isDragging]);

    const handleKeyUp = useCallback((e) => {
        if (e.code === 'Space') {
            setIsSpacePressed(false);
            if (canvasRef.current && !isDragging) canvasRef.current.style.cursor = 'default';
        }
    }, [isDragging]);

    // --- 事件监听器设置 ---
    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;
        container.addEventListener('wheel', handleWheel, { passive: false });
        window.addEventListener('keydown', handleKeyDown);
        window.addEventListener('keyup', handleKeyUp);
        container.addEventListener('mousedown', handleMouseDown);
        window.addEventListener('mousemove', handleMouseMove);
        window.addEventListener('mouseup', handleMouseUp);

        return () => {
            container.removeEventListener('wheel', handleWheel);
            window.removeEventListener('keydown', handleKeyDown);
            window.removeEventListener('keyup', handleKeyUp);
            container.removeEventListener('mousedown', handleMouseDown);
            window.removeEventListener('mousemove', handleMouseMove);
            window.removeEventListener('mouseup', handleMouseUp);
        };
    }, [handleWheel, handleKeyDown, handleKeyUp, handleMouseDown, handleMouseMove, handleMouseUp]);

    // Canvas 的 Resize observer
    useEffect(() => {
        const canvas = canvasRef.current;
        const container = containerRef.current;
        if (!canvas || !container) return;

        const resizeObserver = new ResizeObserver(() => {
            const { width, height } = container.getBoundingClientRect();
            const dpr = window.devicePixelRatio || 1;
            canvas.width = width * dpr;
            canvas.height = height * dpr;
            canvas.style.width = `${width}px`;
            canvas.style.height = `${height}px`;
            const ctx = canvas.getContext('2d');
            ctx.scale(dpr, dpr);
            draw();
        });

        resizeObserver.observe(container);
        return () => resizeObserver.disconnect();
    }, [draw]);

    return (
        <div ref={containerRef} className="w-full h-full bg-gray-800">
            <canvas ref={canvasRef} className="absolute top-0 left-0 w-full h-full" />
            <div className="absolute top-2 right-4 flex flex-col gap-2 z-20">
                <p className="text-xs font-mono text-amber-400 mt-1 bg-black bg-opacity-50 px-2 py-1 rounded">
                    缩放: {scale.toFixed(3)}
                </p>
            </div>
        </div>
    );
});

function findNearestNode(nodes, x, y) {
    let nearestNode = null;
    let minDistance = Infinity;

    nodes.forEach(node => {
        const distance = Math.sqrt(Math.pow(x - node.x, 2) + Math.pow(y - node.y, 2));
        if (distance < minDistance) {
            minDistance = distance;
            nearestNode = node.id;
        }
    });

    return nearestNode;
}
