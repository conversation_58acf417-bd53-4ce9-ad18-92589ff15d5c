import { useState, useEffect } from 'react';
import { analyzeMapData } from '../utils/mapAnalysis';

// 图标组件
const WarningIcon = () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
    </svg>
);

const CheckIcon = () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
    </svg>
);

const LocationIcon = () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
    </svg>
);

const RefreshIcon = () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
    </svg>
);

export const AnomalyChecker = ({ mapData, onHighlightElement }) => {
    const [analysisResult, setAnalysisResult] = useState(null);
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [expandedSections, setExpandedSections] = useState({
        isolatedNodes: false,
        isolatedIslands: false,
        deadEndNodes: false
    });

    // 分析地图数据
    const analyzeMap = () => {
        setIsAnalyzing(true);
        
        // 模拟异步分析过程
        setTimeout(() => {
            const result = analyzeMapData(mapData);
            setAnalysisResult(result);
            setIsAnalyzing(false);
        }, 500);
    };

    // 组件挂载时自动分析
    useEffect(() => {
        if (mapData) {
            analyzeMap();
        }
    }, [mapData]);

    // 切换展开状态
    const toggleSection = (section) => {
        setExpandedSections(prev => ({
            ...prev,
            [section]: !prev[section]
        }));
    };

    // 跳转到元素
    const handleJumpToElement = (element) => {
        if (element.type === 'isolated_node' || element.type === 'dead_end_node') {
            onHighlightElement?.({
                type: 'node',
                data: element.node
            });
        } else if (element.type === 'isolated_island') {
            // 跳转到岛屿的第一个节点
            if (element.nodes && element.nodes.length > 0) {
                onHighlightElement?.({
                    type: 'node',
                    data: element.nodes[0]
                });
            }
        }
    };

    // 获取问题类型的颜色
    const getIssueTypeColor = (type) => {
        switch (type) {
            case 'isolated_node':
                return 'text-red-600 bg-red-50';
            case 'isolated_island':
                return 'text-orange-600 bg-orange-50';
            case 'dead_end_node':
                return 'text-yellow-600 bg-yellow-50';
            default:
                return 'text-gray-600 bg-gray-50';
        }
    };

    // 获取问题类型的图标
    const getIssueTypeIcon = (type) => {
        switch (type) {
            case 'isolated_node':
            case 'isolated_island':
            case 'dead_end_node':
                return <WarningIcon />;
            default:
                return <WarningIcon />;
        }
    };

    if (!analysisResult) {
        return (
            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-gray-900">异常检查</h3>
                    <button
                        onClick={analyzeMap}
                        disabled={isAnalyzing}
                        className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50"
                    >
                        <RefreshIcon />
                        {isAnalyzing ? '分析中...' : '开始分析'}
                    </button>
                </div>
                
                {isAnalyzing && (
                    <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                        <p className="text-sm text-gray-600">正在分析地图数据...</p>
                    </div>
                )}
            </div>
        );
    }

    const { isolatedNodes, isolatedIslands, deadEndNodes, summary } = analysisResult;

    return (
        <div className="space-y-4">
            {/* 头部和重新分析按钮 */}
            <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-900">异常检查</h3>
                <button
                    onClick={analyzeMap}
                    disabled={isAnalyzing}
                    className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50"
                >
                    <RefreshIcon />
                    重新分析
                </button>
            </div>

            {/* 分析摘要 */}
            <div className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                    {summary.hasIssues ? (
                        <WarningIcon className="text-orange-500" />
                    ) : (
                        <CheckIcon className="text-green-500" />
                    )}
                    <span className="text-sm font-medium">
                        {summary.hasIssues ? `发现 ${summary.issueCount} 个问题` : '未发现异常'}
                    </span>
                </div>
                <div className="text-xs text-gray-600 space-y-1">
                    <p>总节点数: {summary.totalNodes}</p>
                    <p>总连接数: {summary.totalEdges}</p>
                </div>
            </div>

            {/* 问题列表 */}
            {summary.hasIssues && (
                <div className="space-y-3">
                    {/* 孤立节点 */}
                    {isolatedNodes.length > 0 && (
                        <div className="border border-red-200 rounded-lg">
                            <button
                                onClick={() => toggleSection('isolatedNodes')}
                                className="w-full flex items-center justify-between p-3 text-left hover:bg-red-50"
                            >
                                <div className="flex items-center gap-2">
                                    <WarningIcon className="text-red-500" />
                                    <span className="text-sm font-medium text-red-700">
                                        孤立节点 ({isolatedNodes.length})
                                    </span>
                                </div>
                                <svg 
                                    className={`w-4 h-4 text-red-500 transition-transform ${expandedSections.isolatedNodes ? 'rotate-180' : ''}`}
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            
                            {expandedSections.isolatedNodes && (
                                <div className="border-t border-red-200 p-3 space-y-2">
                                    {isolatedNodes.map((node, index) => (
                                        <div key={index} className="flex items-center justify-between p-2 bg-red-50 rounded">
                                            <div>
                                                <p className="text-sm font-medium text-red-800">节点 {node.id}</p>
                                                <p className="text-xs text-red-600">{node.description}</p>
                                            </div>
                                            <button
                                                onClick={() => handleJumpToElement(node)}
                                                className="flex items-center gap-1 px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
                                            >
                                                <LocationIcon />
                                                定位
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    )}

                    {/* 孤立岛屿 */}
                    {isolatedIslands.length > 0 && (
                        <div className="border border-orange-200 rounded-lg">
                            <button
                                onClick={() => toggleSection('isolatedIslands')}
                                className="w-full flex items-center justify-between p-3 text-left hover:bg-orange-50"
                            >
                                <div className="flex items-center gap-2">
                                    <WarningIcon className="text-orange-500" />
                                    <span className="text-sm font-medium text-orange-700">
                                        孤立岛屿 ({isolatedIslands.length})
                                    </span>
                                </div>
                                <svg 
                                    className={`w-4 h-4 text-orange-500 transition-transform ${expandedSections.isolatedIslands ? 'rotate-180' : ''}`}
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            
                            {expandedSections.isolatedIslands && (
                                <div className="border-t border-orange-200 p-3 space-y-2">
                                    {isolatedIslands.map((island, index) => (
                                        <div key={index} className="flex items-center justify-between p-2 bg-orange-50 rounded">
                                            <div>
                                                <p className="text-sm font-medium text-orange-800">岛屿 {island.id}</p>
                                                <p className="text-xs text-orange-600">{island.description}</p>
                                            </div>
                                            <button
                                                onClick={() => handleJumpToElement(island)}
                                                className="flex items-center gap-1 px-2 py-1 text-xs bg-orange-100 text-orange-700 rounded hover:bg-orange-200"
                                            >
                                                <LocationIcon />
                                                定位
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    )}

                    {/* 死胡同节点 */}
                    {deadEndNodes.length > 0 && (
                        <div className="border border-yellow-200 rounded-lg">
                            <button
                                onClick={() => toggleSection('deadEndNodes')}
                                className="w-full flex items-center justify-between p-3 text-left hover:bg-yellow-50"
                            >
                                <div className="flex items-center gap-2">
                                    <WarningIcon className="text-yellow-500" />
                                    <span className="text-sm font-medium text-yellow-700">
                                        死胡同节点 ({deadEndNodes.length})
                                    </span>
                                </div>
                                <svg 
                                    className={`w-4 h-4 text-yellow-500 transition-transform ${expandedSections.deadEndNodes ? 'rotate-180' : ''}`}
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            
                            {expandedSections.deadEndNodes && (
                                <div className="border-t border-yellow-200 p-3 space-y-2">
                                    {deadEndNodes.map((node, index) => (
                                        <div key={index} className="flex items-center justify-between p-2 bg-yellow-50 rounded">
                                            <div>
                                                <p className="text-sm font-medium text-yellow-800">节点 {node.id}</p>
                                                <p className="text-xs text-yellow-600">{node.description}</p>
                                            </div>
                                            <button
                                                onClick={() => handleJumpToElement(node)}
                                                className="flex items-center gap-1 px-2 py-1 text-xs bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200"
                                            >
                                                <LocationIcon />
                                                定位
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    )}
                </div>
            )}

            {/* 无问题状态 */}
            {!summary.hasIssues && (
                <div className="text-center py-6 text-gray-500">
                    <CheckIcon className="w-12 h-12 mx-auto text-green-500 mb-2" />
                    <p className="text-sm">地图数据正常，未发现异常</p>
                </div>
            )}
        </div>
    );
};
