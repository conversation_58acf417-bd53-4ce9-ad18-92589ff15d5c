import { useState } from 'react';

export const SchemeManager = ({ schemes, onSchemeUpdate, onSchemeDelete, onSchemeCreate }) => {
    const [isCreating, setIsCreating] = useState(false);
    const [editingScheme, setEditingScheme] = useState(null);
    const [showPreview, setShowPreview] = useState(null);
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        status: 'draft',
        mapFile: null,
        roadFile: null
    });
    const [uploadedFiles, setUploadedFiles] = useState({
        mapData: null,
        roadData: null
    });

    const handleCreateNew = () => {
        setFormData({
            name: '',
            description: '',
            status: 'draft',
            mapFile: null,
            roadFile: null
        });
        setUploadedFiles({
            mapData: null,
            roadData: null
        });
        setIsCreating(true);
    };

    // 处理文件上传
    const handleFileUpload = async (file, type) => {
        if (!file) return;

        try {
            const text = await file.text();
            const data = JSON.parse(text);

            setUploadedFiles(prev => ({
                ...prev,
                [type]: data
            }));

            setFormData(prev => ({
                ...prev,
                [type === 'mapData' ? 'mapFile' : 'roadFile']: file.name
            }));
        } catch (error) {
            alert('文件格式错误，请上传有效的JSON文件');
        }
    };

    // 预览方案
    const handlePreview = (scheme) => {
        setShowPreview(scheme);
    };

    const handleEdit = (scheme) => {
        setFormData({
            name: scheme.name,
            description: scheme.description,
            status: scheme.status,
            mapFile: null,
            roadFile: null
        });
        setUploadedFiles({
            mapData: null,
            roadData: null
        });
        setEditingScheme(scheme);
    };

    const handleSave = () => {
        if (!formData.name.trim()) {
            alert('请输入方案名称');
            return;
        }

        if (isCreating) {
            const newScheme = {
                id: `scheme-${Date.now()}`,
                name: formData.name,
                description: formData.description,
                status: formData.status,
                mapDataUrl: uploadedFiles.mapData ? `/schemes/${formData.name}_map.json` : '/map_data.json',
                roadDataUrl: uploadedFiles.roadData ? `/schemes/${formData.name}_road.json` : '/road_data.json',
                createdAt: new Date().toISOString().split('T')[0],
                thumbnail: '/thumbnails/default.png',
                hasCustomData: !!(uploadedFiles.mapData || uploadedFiles.roadData)
            };

            // 在实际应用中，这里会上传文件到服务器
            console.log('新建方案:', newScheme);
            console.log('上传的文件:', uploadedFiles);

            onSchemeCreate?.(newScheme);
            setIsCreating(false);
        } else if (editingScheme) {
            const updates = {
                name: formData.name,
                description: formData.description,
                status: formData.status
            };

            // 如果有新上传的文件，更新数据URL
            if (uploadedFiles.mapData) {
                updates.mapDataUrl = `/schemes/${formData.name}_map.json`;
                updates.hasCustomData = true;
            }
            if (uploadedFiles.roadData) {
                updates.roadDataUrl = `/schemes/${formData.name}_road.json`;
                updates.hasCustomData = true;
            }

            console.log('更新方案:', editingScheme.id, updates);
            console.log('上传的文件:', uploadedFiles);

            onSchemeUpdate?.(editingScheme.id, updates);
            setEditingScheme(null);
        }

        setFormData({ name: '', description: '', status: 'draft', mapFile: null, roadFile: null });
        setUploadedFiles({ mapData: null, roadData: null });
    };

    const handleCancel = () => {
        setIsCreating(false);
        setEditingScheme(null);
        setShowPreview(null);
        setFormData({ name: '', description: '', status: 'draft', mapFile: null, roadFile: null });
        setUploadedFiles({ mapData: null, roadData: null });
    };

    const handleDelete = (schemeId) => {
        if (window.confirm('确定要删除这个方案吗？此操作不可撤销。')) {
            onSchemeDelete?.(schemeId);
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'draft': return 'bg-yellow-100 text-yellow-800';
            case 'archived': return 'bg-gray-100 text-gray-800';
            default: return 'bg-blue-100 text-blue-800';
        }
    };

    const getStatusText = (status) => {
        switch (status) {
            case 'active': return '已激活';
            case 'draft': return '草稿';
            case 'archived': return '已归档';
            default: return '未知';
        }
    };

    return (
        <div className="space-y-4">
            {/* 头部操作 */}
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-white">方案管理</h3>
                <button
                    onClick={handleCreateNew}
                    className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                >
                    新建方案
                </button>
            </div>

            {/* 创建/编辑表单 */}
            {(isCreating || editingScheme) && (
                <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
                    <h4 className="text-white font-medium mb-3">
                        {isCreating ? '创建新方案' : '编辑方案'}
                    </h4>
                    
                    <div className="space-y-3">
                        <div>
                            <label className="block text-sm text-gray-300 mb-1">方案名称</label>
                            <input
                                type="text"
                                value={formData.name}
                                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                                className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
                                placeholder="输入方案名称"
                            />
                        </div>
                        
                        <div>
                            <label className="block text-sm text-gray-300 mb-1">描述</label>
                            <textarea
                                value={formData.description}
                                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                                className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
                                rows="2"
                                placeholder="输入方案描述"
                            />
                        </div>
                        
                        <div>
                            <label className="block text-sm text-gray-300 mb-1">状态</label>
                            <select
                                value={formData.status}
                                onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                                className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
                            >
                                <option value="draft">草稿</option>
                                <option value="active">已激活</option>
                                <option value="archived">已归档</option>
                            </select>
                        </div>

                        {/* 文件上传 */}
                        <div className="space-y-3 pt-3 border-t border-gray-600">
                            <h4 className="text-sm font-medium text-gray-300">数据文件上传（可选）</h4>

                            <div>
                                <label className="block text-sm text-gray-300 mb-1">地图数据文件 (map_data.json)</label>
                                <input
                                    type="file"
                                    accept=".json"
                                    onChange={(e) => handleFileUpload(e.target.files[0], 'mapData')}
                                    className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none file:mr-4 file:py-1 file:px-2 file:rounded file:border-0 file:text-sm file:bg-blue-600 file:text-white hover:file:bg-blue-700"
                                />
                                {formData.mapFile && (
                                    <p className="text-xs text-green-400 mt-1">已选择: {formData.mapFile}</p>
                                )}
                            </div>

                            <div>
                                <label className="block text-sm text-gray-300 mb-1">道路数据文件 (road_data.json)</label>
                                <input
                                    type="file"
                                    accept=".json"
                                    onChange={(e) => handleFileUpload(e.target.files[0], 'roadData')}
                                    className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none file:mr-4 file:py-1 file:px-2 file:rounded file:border-0 file:text-sm file:bg-blue-600 file:text-white hover:file:bg-blue-700"
                                />
                                {formData.roadFile && (
                                    <p className="text-xs text-green-400 mt-1">已选择: {formData.roadFile}</p>
                                )}
                            </div>

                            <p className="text-xs text-gray-400">
                                如果不上传文件，将使用默认的演示数据
                            </p>
                        </div>
                    </div>
                    
                    <div className="flex space-x-2 mt-4">
                        <button
                            onClick={handleSave}
                            disabled={!formData.name.trim()}
                            className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors"
                        >
                            保存
                        </button>
                        <button
                            onClick={handleCancel}
                            className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 transition-colors"
                        >
                            取消
                        </button>
                    </div>
                </div>
            )}

            {/* 方案列表 */}
            <div className="space-y-2">
                {schemes.map((scheme) => (
                    <div
                        key={scheme.id}
                        className="bg-gray-800 p-3 rounded border border-gray-700 hover:border-gray-600 transition-colors"
                    >
                        <div className="flex items-center justify-between">
                            <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                    <h4 className="text-white font-medium text-sm">{scheme.name}</h4>
                                    <span className={`px-2 py-0.5 text-xs font-medium rounded ${getStatusColor(scheme.status)}`}>
                                        {getStatusText(scheme.status)}
                                    </span>
                                </div>
                                <p className="text-gray-400 text-xs">{scheme.description}</p>
                                <p className="text-gray-500 text-xs mt-1">创建: {scheme.createdAt}</p>
                            </div>
                            
                            <div className="flex space-x-1">
                                <button
                                    onClick={() => handlePreview(scheme)}
                                    className="p-1 text-green-400 hover:text-green-300 transition-colors"
                                    title="预览"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </button>
                                <button
                                    onClick={() => handleEdit(scheme)}
                                    className="p-1 text-blue-400 hover:text-blue-300 transition-colors"
                                    title="编辑"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                </button>
                                <button
                                    onClick={() => handleDelete(scheme.id)}
                                    className="p-1 text-red-400 hover:text-red-300 transition-colors"
                                    title="删除"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {schemes.length === 0 && (
                <div className="text-center py-8 text-gray-400">
                    <p className="text-sm">暂无方案</p>
                    <p className="text-xs mt-1">点击"新建方案"开始创建</p>
                </div>
            )}

            {/* 预览模态框 */}
            {showPreview && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-semibold text-gray-900">方案预览</h3>
                            <button
                                onClick={() => setShowPreview(null)}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <div className="space-y-3">
                            <div>
                                <label className="text-sm font-medium text-gray-700">方案名称</label>
                                <p className="text-gray-900">{showPreview.name}</p>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-700">描述</label>
                                <p className="text-gray-900">{showPreview.description}</p>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-700">状态</label>
                                <span className={`inline-block px-2 py-1 text-xs font-medium rounded ${getStatusColor(showPreview.status)}`}>
                                    {getStatusText(showPreview.status)}
                                </span>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-700">创建时间</label>
                                <p className="text-gray-900">{showPreview.createdAt}</p>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-700">数据文件</label>
                                <div className="text-sm text-gray-600">
                                    <p>地图数据: {showPreview.mapDataUrl}</p>
                                    <p>道路数据: {showPreview.roadDataUrl}</p>
                                    {showPreview.hasCustomData && (
                                        <p className="text-green-600 text-xs mt-1">✓ 包含自定义数据</p>
                                    )}
                                </div>
                            </div>
                        </div>

                        <div className="flex justify-end mt-6">
                            <button
                                onClick={() => setShowPreview(null)}
                                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
                            >
                                关闭
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};
