# FactoryTwin 渲染逻辑和查看详情功能说明

## 整体架构概览

```
App.jsx (主应用)
├── Navigation.jsx (左侧导航)
├── CanvasRenderer.jsx (中间地图渲染)
└── DetailPanel.jsx (右侧详情面板)

数据流:
useMapData → useDynamicState → CanvasRenderer → DetailPanel
```

## 1. 数据管理层

### 1.1 静态地图数据 (`useMapData`)
- **文件**: `src/hooks/useMapData.js`
- **职责**: 加载和处理静态地图数据
- **数据结构**:
  ```javascript
  {
    nodes: Map<nodeId, nodeData>,
    unidirectional_edges: Array,
    bidirectional_edges: Array,
    graph_info: Object
  }
  ```
- **注意**: 用户的map_data.json中不包含vehicles和pallets数据，这些由动态状态管理单独处理

### 1.2 动态状态管理 (`useDynamicState`)
- **文件**: `src/hooks/useDynamicState.js`
- **职责**: 管理小车和托盘的实时状态
- **初始化**: 创建示例小车和托盘数据（因为静态地图数据中不包含这些）
- **核心功能**:
  - 创建和管理动态状态
  - 提供状态更新接口
  - 计算带位置信息的动态元素
- **状态结构**:
  ```javascript
  {
    cars: [
      {
        id, name, status, battery, location,
        isRunning, isRotating, rotationAngle, speed, targetPosition, currentTask
      }
    ],
    pallets: [
      {
        id, shape, status, contents, weight, location,
        isMoving, isLoaded, currentCarrier, targetLocation
      }
    ],
    lastUpdateTime: timestamp
  }
  ```
- **预留接口**:
  ```javascript
  updateDynamicStateFromJson(jsonData)  // 从JSON更新整体状态
  updateCarState(carId, newState)       // 更新单个小车状态
  updatePalletState(palletId, newState) // 更新单个托盘状态
  ```

### 1.3 仿真控制 (`useSimulation`)
- **文件**: `src/hooks/useSimulation.js`
- **职责**: 管理路径规划和时间轴控制
- **功能**: 路线计算、播放控制、时间管理

## 2. 渲染层

### 2.1 CanvasRenderer 组件
- **文件**: `src/components/CanvasRenderer.jsx`
- **职责**: Canvas绘制和交互处理

#### 渲染顺序 (draw函数):
1. **清空画布**
2. **设置变换** (平移、缩放)
3. **绘制双向边** 
   - 遍历 `mapData.bidirectional_edges`
   - 检查选中状态: `selectedElement?.type === 'edge' && selectedElement?.data?.id === edgeId`
4. **绘制单向边**
   - 遍历 `mapData.unidirectional_edges`
   - 根据缩放级别决定是否显示箭头
5. **绘制节点**
   - 遍历 `mapData.nodes`
   - 检查选中状态: `selectedElement?.type === 'node' && selectedElement?.data?.id === nodeId`
6. **绘制动态元素** (可控制显示/隐藏)
   - 仅在 `showDynamicElements=true` 时显示
   - 绘制托盘: `dynamicElements.pallets`
   - 绘制小车: `dynamicElements.cars`

#### 关键参数:
- `showDynamicElements`: 控制是否显示动态元素
- `selectedElement`: 当前选中的元素
- `onElementClick`: 点击回调函数

## 3. 点击检测逻辑

### 3.1 检测流程 (`handleElementClick`)
```javascript
// 1. 坐标转换
const worldX = (mouseX - offset.x) / scale;
const worldY = (mouseY - offset.y) / scale;

// 2. 检测优先级 (从高到低)
if (节点被点击) {
    return { type: 'node', data: {...} };
}
else if (动态元素被点击 && showDynamicElements) {
    if (小车被点击) return { type: 'car', data: {...} };
    if (托盘被点击) return { type: 'pallet', data: {...} };
}
else if (边被点击) {
    if (单向边被点击) return { type: 'edge', data: {...} };
    if (双向边被点击) return { type: 'edge', data: {...} };
}
```

### 3.2 检测半径
- **节点**: 15 / scale
- **小车**: 15 / scale  
- **托盘**: 20 / scale
- **边**: 10 / scale

### 3.3 数据结构统一化
所有点击返回的数据都遵循统一格式:
```javascript
{
    type: 'node' | 'edge' | 'car' | 'pallet',
    data: {
        id: string,
        x: number,
        y: number,
        ...其他属性
    }
}
```

## 4. 详情显示逻辑

### 4.1 DetailPanel 组件
- **文件**: `src/components/DetailPanel.jsx`
- **输入**: `selectedElement` (来自点击检测)

### 4.2 显示逻辑
```javascript
if (!selectedElement) {
    显示空状态提示
} else {
    根据 selectedElement.type 显示对应信息:
    - 'node': 节点信息 (ID, 坐标, 类型, 设备信息)
    - 'edge': 边信息 (起点, 终点, 权重, 类型)
    - 'car': AGV信息 (角度, 速度, 状态, 电池)
    - 'pallet': 托盘信息 (形状, 重量, 内容, 状态)
}
```

## 5. 组件间通信

### 5.1 数据流向
```
App.jsx
├── mapData (静态) → CanvasRenderer
├── dynamicElements (动态) → CanvasRenderer  
├── selectedElement (状态) → CanvasRenderer & DetailPanel
└── onElementClick (回调) → CanvasRenderer
```

### 5.2 事件流
1. **用户点击地图** → `CanvasRenderer.handleElementClick`
2. **检测点击元素** → 生成 `clickedElement` 对象
3. **调用回调** → `onElementClick(clickedElement)`
4. **更新状态** → `setSelectedElement(clickedElement)`
5. **触发重渲染** → `DetailPanel` 显示详情

## 6. 关键修复点

### 6.1 选中状态检查修复
**问题**: 边和节点的选中状态检查不一致
**修复**: 统一使用 `selectedElement?.type === 'xxx' && selectedElement?.data?.id === xxxId`

### 6.2 动态元素显示控制
**问题**: 静态地图中显示了动态元素
**修复**: 添加 `showDynamicElements` 参数控制显示

### 6.3 ID生成统一化
**问题**: 边的ID生成逻辑不一致
**修复**: 统一使用 `edge.id || 'startNode-endNode'` 格式

## 7. 预留接口说明

### 7.1 动态状态更新接口
```javascript
// 从JSON更新整体状态
updateDynamicStateFromJson({
    cars: [{ id, isRunning, rotationAngle, speed, ... }],
    pallets: [{ id, isMoving, isLoaded, ... }]
});

// 更新单个元素
updateCarState('agv_001', { 
    isRunning: true, 
    rotationAngle: 45, 
    speed: 2.5 
});
```

### 7.2 动态状态字段
**小车状态**:
- `isRunning`: 是否在运行
- `isRotating`: 是否在旋转  
- `rotationAngle`: 旋转角度
- `speed`: 当前速度
- `targetPosition`: 目标位置
- `currentTask`: 当前任务

**托盘状态**:
- `isMoving`: 是否在移动
- `isLoaded`: 是否已装载
- `currentCarrier`: 当前承载车辆
- `targetLocation`: 目标位置

这些接口为后续接入实时数据源提供了完整的支持。
